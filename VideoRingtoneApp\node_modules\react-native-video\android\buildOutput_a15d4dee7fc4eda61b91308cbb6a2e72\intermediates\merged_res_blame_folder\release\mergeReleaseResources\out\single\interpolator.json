[{"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/fast_out_slow_in.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/fast_out_slow_in.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}]