# 🐛 Debug Guide for Video Ringtone App

## 📱 APK Files for Testing

### Latest Version (Dialer Fixed)
- **VideoRingtoneApp-FINAL-DIALER-release.apk** (50.68 MB) - **RECOMMENDED FOR TESTING**
- **VideoRingtoneApp-DIALER-FIXED-debug.apk** (116.66 MB) - **FOR DEBUGGING**

## 🔧 Debug Mode Setup

### Prerequisites
1. **Enable Developer Options** on your Android device:
   - Go to Settings > About phone
   - Tap "Build number" 7 times
   - Developer options will appear in Settings

2. **Enable USB Debugging**:
   - Settings > Developer options > USB debugging (ON)

3. **Install ADB** on your PC:
   - Download Android SDK Platform Tools
   - Add to your system PATH

### Debug APK Installation
```bash
# Install debug APK with ADB
adb install VideoRingtoneApp-DIALER-FIXED-debug.apk

# Or force reinstall if already installed
adb install -r VideoRingtoneApp-DIALER-FIXED-debug.apk
```

## 📊 Real-time Debugging

### 1. View Live Logs
```bash
# View all app logs
adb logcat | grep VideoRingtone

# View specific component logs
adb logcat | grep "VideoRingtoneInCallService"
adb logcat | grep "VideoRingtoneModule"
adb logcat | grep "VideoRingtoneService"

# View dialer-related logs
adb logcat | grep -E "(Telecom|InCall|Dialer)"

# Clear logs and start fresh
adb logcat -c
adb logcat | grep VideoRingtone
```

### 2. Check Default Dialer Status
```bash
# Check if app is recognized as dialer
adb shell dumpsys telecom | grep -i "default"

# Check available dialer apps
adb shell cmd role get-role-holders android.app.role.DIALER

# Check if app has dialer role
adb shell cmd role get-role-holders android.app.role.DIALER | grep videoringtone
```

### 3. Permission Debugging
```bash
# Check all app permissions
adb shell dumpsys package com.videoringtoneapp | grep permission

# Check specific permissions
adb shell pm list permissions -d | grep -E "(PHONE|CALL|DIALER)"

# Grant permissions manually (if needed)
adb shell pm grant com.videoringtoneapp android.permission.READ_PHONE_STATE
adb shell pm grant com.videoringtoneapp android.permission.CALL_PHONE
adb shell pm grant com.videoringtoneapp android.permission.ANSWER_PHONE_CALLS
```

## 🔍 Troubleshooting Steps

### Issue: App Not Showing in Default Dialer List

**Check 1: InCallService Registration**
```bash
adb logcat | grep "InCallService"
```
Expected: Should see "InCallService created" when app starts

**Check 2: Intent Filters**
```bash
adb shell dumpsys package com.videoringtoneapp | grep -A 10 "intent-filter"
```
Expected: Should see ACTION_DIAL and tel: scheme filters

**Check 3: Service Binding**
```bash
adb shell dumpsys activity services | grep VideoRingtone
```

### Issue: Permissions Not Working

**Check Permission Status:**
```bash
# Check all permissions
adb shell dumpsys package com.videoringtoneapp | grep "android.permission"

# Check specific permission
adb shell pm list permissions -g | grep PHONE
```

**Force Grant Permissions:**
```bash
adb shell pm grant com.videoringtoneapp android.permission.READ_MEDIA_VIDEO
adb shell pm grant com.videoringtoneapp android.permission.READ_EXTERNAL_STORAGE
```

### Issue: Video Ringtone Not Playing

**Check Service Status:**
```bash
adb logcat | grep "VideoRingtoneService"
```

**Check File Access:**
```bash
adb logcat | grep -E "(MediaPlayer|VideoView|FileNotFound)"
```

## 📱 Device-Specific Debugging

### Vivo Devices (iQOO Neo 7 Pro)
```bash
# Check Vivo-specific restrictions
adb shell dumpsys deviceidle | grep -i vivo
adb shell dumpsys battery | grep optimization

# Check auto-start permissions
adb shell dumpsys usagestats | grep videoringtone
```

### Android 13+ Debugging
```bash
# Check media permissions
adb shell dumpsys package com.videoringtoneapp | grep READ_MEDIA

# Check notification permissions
adb shell dumpsys notification | grep videoringtone
```

## 🚨 Common Issues & Solutions

### 1. "App not appearing in dialer list"
**Solution:** Check InCallService implementation
```bash
adb logcat | grep "BIND_INCALL_SERVICE"
```

### 2. "Permissions keep asking"
**Solution:** Check Android 13+ media permissions
```bash
adb shell pm grant com.videoringtoneapp android.permission.READ_MEDIA_VIDEO
```

### 3. "Video not playing"
**Solution:** Check file permissions and media player logs
```bash
adb logcat | grep -E "(MediaPlayer|VideoView|permission denied)"
```

## 📋 Debug Checklist

Before reporting issues, verify:

- [ ] Debug APK installed correctly
- [ ] USB debugging enabled
- [ ] All permissions granted
- [ ] InCallService logs appearing
- [ ] Device-specific settings checked (Vivo)
- [ ] Android version compatibility (API 24+)

## 🔧 Advanced Debugging

### Enable Verbose Logging
```bash
# Enable all telecom logs
adb shell setprop log.tag.Telecom VERBOSE
adb shell setprop log.tag.TelecomManager VERBOSE

# Restart telecom service
adb shell am force-stop com.android.server.telecom
```

### Capture Bug Report
```bash
# Generate full bug report
adb bugreport bugreport.zip
```

## 📞 Test Scenarios

### Test Default Dialer Setup
1. Install debug APK
2. Open app
3. Tap "Set as Default Dialer"
4. Check if app appears in Settings > Apps > Default apps > Phone app

### Test Incoming Call Detection
1. Set app as default dialer
2. Call the device from another phone
3. Check logs for InCallService activity
4. Verify video ringtone plays

### Test Permissions
1. Revoke all permissions
2. Use app permission buttons
3. Verify each permission is granted
4. Test functionality after each permission

## 📝 Log Analysis

### Key Log Messages to Look For:
- `InCallService created` - Service started correctly
- `Call added` - Incoming call detected
- `VideoRingtoneService started` - Video playback initiated
- `Permission granted` - Permissions working
- `Default dialer set` - Dialer role assigned

### Error Messages to Watch For:
- `SecurityException` - Permission issues
- `ServiceConnectionException` - Service binding problems
- `FileNotFoundException` - Video file access issues
- `IllegalStateException` - App state problems
