<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-fs" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-video" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-permissions" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-permissions\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\assets"><file name="index.android.bundle" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\assets\index.android.bundle"/><file name="sample_video.txt" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\assets\sample_video.txt"/></source><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons"><file name="fonts/AntDesign.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\AntDesign.ttf"/><file name="fonts/Entypo.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Entypo.ttf"/><file name="fonts/EvilIcons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\EvilIcons.ttf"/><file name="fonts/Feather.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Feather.ttf"/><file name="fonts/FontAwesome.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Brands.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Regular.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome6_Brands.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Brands.ttf"/><file name="fonts/FontAwesome6_Regular.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Regular.ttf"/><file name="fonts/FontAwesome6_Solid.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Solid.ttf"/><file name="fonts/Fontisto.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Fontisto.ttf"/><file name="fonts/Foundation.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialIcons.ttf"/><file name="fonts/Octicons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Octicons.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\SimpleLineIcons.ttf"/><file name="fonts/Zocial.ttf" path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Zocial.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>