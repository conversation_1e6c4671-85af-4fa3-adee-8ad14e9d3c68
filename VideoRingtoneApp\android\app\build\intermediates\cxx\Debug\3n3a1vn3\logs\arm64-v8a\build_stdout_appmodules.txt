ninja: Entering directory `H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\Debug\3n3a1vn3\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a
[0/2] Re-checking globbed directories...
[1/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1b7494b9201882dd9ab653dea491eea7/jni/react/renderer/components/rnscreens/States.cpp.o
[2/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/84810466fed9ae02a4d01bd98d9e41bc/components/safeareacontext/States.cpp.o
[3/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/EventEmitters.cpp.o
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:16:69: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   16 | void RNCSafeAreaProviderEventEmitter::onInsetsChange(OnInsetsChange $event) const {
      |                                                                     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:34: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                  ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 |   dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   18 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:21:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   21 |   insets.setProperty(runtime, "top", $event.insets.top);
      |                                      ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:22:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   22 |   insets.setProperty(runtime, "right", $event.insets.right);
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:23:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   23 |   insets.setProperty(runtime, "bottom", $event.insets.bottom);
      |                                         ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:24:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   24 |   insets.setProperty(runtime, "left", $event.insets.left);
      |                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:25:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   25 |   $payload.setProperty(runtime, "insets", insets);
      |   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:29:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   29 |   frame.setProperty(runtime, "x", $event.frame.x);
      |                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:30:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   30 |   frame.setProperty(runtime, "y", $event.frame.y);
      |                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:31:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 |   frame.setProperty(runtime, "width", $event.frame.width);
      |                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:32:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   frame.setProperty(runtime, "height", $event.frame.height);
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:33:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |   $payload.setProperty(runtime, "frame", frame);
      |   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:35:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   35 |     return $payload;
      |            ^
15 warnings generated.
[4/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/rnscreensJSI-generated.cpp.o
[5/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03de314beaa0d70218a6841911723387/safeareacontextJSI-generated.cpp.o
[6/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cfc0f21988c7424a6729bc3fef3cc917/renderer/components/rnscreens/RNSScreenState.cpp.o
[7/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fd6c6810e08ab9c131025d6d45a0ab59/jni/safeareacontext-generated.cpp.o
[8/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[9/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/982db2f69122ee290771c1a77076171b/safeareacontext/RNCSafeAreaViewState.cpp.o
[10/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/ShadowNodes.cpp.o
[11/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a7a5b6ef63fe8620daea19ac4f5e9acc/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[12/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e51082ca2170ab862755d4305da4d538/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[13/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a7a5b6ef63fe8620daea19ac4f5e9acc/components/rnscreens/RNSScreenShadowNode.cpp.o
[14/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[15/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/84810466fed9ae02a4d01bd98d9e41bc/components/safeareacontext/Props.cpp.o
[16/31] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[17/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o
FAILED: safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o 
H:\Coding\android-sdk\ndk\27.1.12297006\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -MD -MT safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o -MF safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0\RNCSafeAreaViewShadowNode.cpp.o.d -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o -c H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp:19:12: error: no member named 'unit' in 'facebook::yoga::StyleLength'
   19 |   if (edge.unit() != Unit::Undefined) {
      |       ~~~~ ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp:22:12: error: no member named 'unit' in 'facebook::yoga::StyleLength'
   22 |   if (axis.unit() != Unit::Undefined) {
      |       ~~~~ ^
2 errors generated.
[18/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1b7494b9201882dd9ab653dea491eea7/jni/react/renderer/components/rnscreens/Props.cpp.o
[19/31] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/ComponentDescriptors.cpp.o
[20/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d005d15700f84c5d28791fb8186f963b/renderer/components/rnscreens/EventEmitters.cpp.o
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:17:52: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   17 | void RNSModalScreenEventEmitter::onAppear(OnAppear $event) const {
      |                                                    ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:19:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   19 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:21:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   21 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:26:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   26 | void RNSModalScreenEventEmitter::onDisappear(OnDisappear $event) const {
      |                                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:28:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   28 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:30:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   30 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:35:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   35 | void RNSModalScreenEventEmitter::onDismissed(OnDismissed $event) const {
      |                                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:37:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:39:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:44:84: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   44 | void RNSModalScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
      |                                                                                    ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   45 |   dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   45 |   dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                             ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:46:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   46 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   47 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   47 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:48:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   48 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:53:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   53 | void RNSModalScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
      |                                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:55:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   55 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:57:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   57 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:62:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   62 | void RNSModalScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
      |                                                                  ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:64:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   64 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:66:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   66 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:71:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   71 | void RNSModalScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
      |                                                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   72 |   dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   72 |   dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                         ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:73:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   73 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 |     $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   74 |     $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   75 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:80:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   80 | void RNSModalScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
      |                                                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   81 |   dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   81 |   dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                         ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:82:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   82 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   83 |     $payload.setProperty(runtime, "progress", $event.progress);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   83 |     $payload.setProperty(runtime, "progress", $event.progress);
      |                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   84 | $payload.setProperty(runtime, "closing", $event.closing);
      | ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   84 | $payload.setProperty(runtime, "closing", $event.closing);
      |                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   85 | $payload.setProperty(runtime, "goingForward", $event.goingForward);
      | ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   85 | $payload.setProperty(runtime, "goingForward", $event.goingForward);
      |                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:86:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   86 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:91:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   91 | void RNSModalScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
      |                                                                  ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:93:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   93 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:95:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   95 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:100:86: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  100 | void RNSModalScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
      |                                                                                      ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:102:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  102 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:104:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  104 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  110 | void RNSScreenEventEmitter::onAppear(OnAppear $event) const {
      |                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  112 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:114:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  114 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:119:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  119 | void RNSScreenEventEmitter::onDisappear(OnDisappear $event) const {
      |                                                     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:121:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  121 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:123:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  123 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:128:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  128 | void RNSScreenEventEmitter::onDismissed(OnDismissed $event) const {
      |                                                     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:129:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  129 |   dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:129:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  129 |   dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:130:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  130 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:131:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  131 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:131:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  131 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:132:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  132 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:137:79: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  137 | void RNSScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
      |                                                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:138:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  138 |   dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:138:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  138 |   dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                             ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:139:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  139 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:140:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  140 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:140:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  140 |     $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  141 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:146:55: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  146 | void RNSScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
      |                                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:148:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  148 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  150 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:155:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  155 | void RNSScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
      |                                                             ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:157:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  157 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:159:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  159 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:164:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  164 | void RNSScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
      |                                                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:165:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  165 |   dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:165:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  165 |   dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                         ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:166:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  166 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:167:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  167 |     $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:167:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  167 |     $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
      |                                                   ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:168:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  168 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:173:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  173 | void RNSScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
      |                                                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:174:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  174 |   dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:174:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  174 |   dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                         ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:175:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  175 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:176:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  176 |     $payload.setProperty(runtime, "progress", $event.progress);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:176:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  176 |     $payload.setProperty(runtime, "progress", $event.progress);
      |                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  177 | $payload.setProperty(runtime, "closing", $event.closing);
      | ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  177 | $payload.setProperty(runtime, "closing", $event.closing);
      |                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:178:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  178 | $payload.setProperty(runtime, "goingForward", $event.goingForward);
      | ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:178:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  178 | $payload.setProperty(runtime, "goingForward", $event.goingForward);
      |                                               ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  179 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:184:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  184 | void RNSScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
      |                                                             ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  186 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  188 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:193:81: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  193 | void RNSScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
      |                                                                                 ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:195:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  195 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:197:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  197 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:203:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  203 | void RNSScreenStackHeaderConfigEventEmitter::onAttached(OnAttached $event) const {
      |                                                                    ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:205:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  205 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:207:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  207 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:212:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  212 | void RNSScreenStackHeaderConfigEventEmitter::onDetached(OnDetached $event) const {
      |                                                                    ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:214:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  214 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:216:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  216 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:222:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  222 | void RNSScreenStackEventEmitter::onFinishTransitioning(OnFinishTransitioning $event) const {
      |                                                                              ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:224:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  224 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:226:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  226 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:231:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  231 | void RNSSearchBarEventEmitter::onSearchFocus(OnSearchFocus $event) const {
      |                                                            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:233:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  233 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:235:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  235 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:240:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  240 | void RNSSearchBarEventEmitter::onSearchBlur(OnSearchBlur $event) const {
      |                                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:242:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  242 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:244:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  244 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:249:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  249 | void RNSSearchBarEventEmitter::onSearchButtonPress(OnSearchButtonPress $event) const {
      |                                                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:250:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  250 |   dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                       ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:250:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  250 |   dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:251:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  251 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:252:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  252 |     $payload.setProperty(runtime, "text", $event.text);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:252:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  252 |     $payload.setProperty(runtime, "text", $event.text);
      |                                           ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:253:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  253 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:258:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  258 | void RNSSearchBarEventEmitter::onCancelButtonPress(OnCancelButtonPress $event) const {
      |                                                                        ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:260:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  260 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:262:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  262 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:267:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  267 | void RNSSearchBarEventEmitter::onChangeText(OnChangeText $event) const {
      |                                                          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:268:32: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  268 |   dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:268:49: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  268 |   dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                                 ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:269:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  269 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:270:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  270 |     $payload.setProperty(runtime, "text", $event.text);
      |     ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:270:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  270 |     $payload.setProperty(runtime, "text", $event.text);
      |                                           ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:271:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  271 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:276:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  276 | void RNSSearchBarEventEmitter::onClose(OnClose $event) const {
      |                                                ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:278:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  278 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:280:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  280 |     return $payload;
      |            ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:285:46: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  285 | void RNSSearchBarEventEmitter::onOpen(OnOpen $event) const {
      |                                              ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:287:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  287 |     auto $payload = jsi::Object(runtime);
      |          ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:289:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  289 |     return $payload;
      |            ^
138 warnings generated.
[21/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[22/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[23/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[24/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[25/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[26/31] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o
[27/31] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/ComponentDescriptors.cpp.o
[28/31] Building CXX object CMakeFiles/appmodules.dir/H_/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
ninja: build stopped: subcommand failed.
