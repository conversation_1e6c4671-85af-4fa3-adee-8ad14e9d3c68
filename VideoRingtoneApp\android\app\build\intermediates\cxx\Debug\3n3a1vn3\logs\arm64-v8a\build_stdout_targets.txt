ninja: Entering directory `H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\Debug\3n3a1vn3\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/4] Linking CXX shared library H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\Debug\3n3a1vn3\obj\arm64-v8a\libreact_codegen_rnscreens.so
[2/4] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o
FAILED: safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o 
H:\Coding\android-sdk\ndk\27.1.12297006\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -MD -MT safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o -MF safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0\RNCSafeAreaViewShadowNode.cpp.o.d -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o -c H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp:19:12: error: no member named 'unit' in 'facebook::yoga::StyleLength'
   19 |   if (edge.unit() != Unit::Undefined) {
      |       ~~~~ ^
H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp:22:12: error: no member named 'unit' in 'facebook::yoga::StyleLength'
   22 |   if (axis.unit() != Unit::Undefined) {
      |       ~~~~ ^
2 errors generated.
ninja: build stopped: subcommand failed.
