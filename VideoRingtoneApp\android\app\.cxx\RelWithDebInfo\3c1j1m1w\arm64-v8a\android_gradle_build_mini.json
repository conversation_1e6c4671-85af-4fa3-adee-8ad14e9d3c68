{"buildFiles": ["H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["H:\\Coding\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\RelWithDebInfo\\3c1j1m1w\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["H:\\Coding\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\RelWithDebInfo\\3c1j1m1w\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\3c1j1m1w\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNPermissionsSpec::@7ad697819b753921c957": {"artifactName": "react_codegen_RNPermissionsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\3c1j1m1w\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\3c1j1m1w\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\3c1j1m1w\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\3c1j1m1w\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72cde7dc85b5006383f56c98fcfedfa5\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}}}