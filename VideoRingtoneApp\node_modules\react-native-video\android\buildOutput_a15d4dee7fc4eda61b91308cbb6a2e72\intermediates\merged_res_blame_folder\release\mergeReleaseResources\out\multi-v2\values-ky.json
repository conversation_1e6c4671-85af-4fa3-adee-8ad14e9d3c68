{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "53,122,123,124,131,133,134", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4169,9767,9836,9919,10479,10628,10700", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "4252,9831,9914,9984,10541,10695,10767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,247,329,419,508,600,679,787,885,970,1039,1148,1235,1328,1421,1535,1615,1716,1787,1856,1937,2022,2112", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "124,242,324,414,503,595,674,782,880,965,1034,1143,1230,1323,1416,1530,1610,1711,1782,1851,1932,2017,2107,2201"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4257,4331,4449,4531,4621,4710,4802,4881,4989,5087,5172,5241,5350,5437,5530,5623,5737,5817,9989,10060,10129,10210,10295,10385", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "4326,4444,4526,4616,4705,4797,4876,4984,5082,5167,5236,5345,5432,5525,5618,5732,5812,5913,10055,10124,10205,10290,10380,10474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1373,1471,1539,1605,1670,1740,1871,2000,2136,2208,2289,2363,2451,2545,2636,2703,2769,2822,2883,2931,2992,3065,3141,3201,3271,3329,3386,3452,3504,3563,3639,3715,3770", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,51,58,75,75,54,66", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1368,1466,1534,1600,1665,1735,1866,1995,2131,2203,2284,2358,2446,2540,2631,2698,2764,2817,2878,2926,2987,3060,3136,3196,3266,3324,3381,3447,3499,3558,3634,3710,3765,3832"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,534,5918,6010,6100,6178,6268,6365,6452,6518,6615,6713,6781,6847,6912,6982,7113,7242,7378,7450,7531,7605,7693,7787,7878,7945,8699,8752,8813,8861,8922,8995,9071,9131,9201,9259,9316,9382,9434,9493,9569,9645,9700", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,51,58,75,75,54,66", "endOffsets": "330,529,721,6005,6095,6173,6263,6360,6447,6513,6610,6708,6776,6842,6907,6977,7108,7237,7373,7445,7526,7600,7688,7782,7873,7940,8006,8747,8808,8856,8917,8990,9066,9126,9196,9254,9311,9377,9429,9488,9564,9640,9695,9762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "726,837,946,1058,1143,1248,1365,1444,1522,1613,1706,1801,1895,1995,2088,2183,2278,2369,2460,2541,2647,2752,2850,2957,3060,3175,3336,10546", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "832,941,1053,1138,1243,1360,1439,1517,1608,1701,1796,1890,1990,2083,2178,2273,2364,2455,2536,2642,2747,2845,2952,3055,3170,3331,3433,10623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3538,3640,3743,3850,3954,4058,10772", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3533,3635,3738,3845,3949,4053,4164,10868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8011,8082,8147,8218,8289,8376,8447,8534,8618", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "8077,8142,8213,8284,8371,8442,8529,8613,8694"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "53,122,123,124,131,133,134", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4169,9767,9836,9919,10479,10628,10700", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "4252,9831,9914,9984,10541,10695,10767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,247,329,419,508,600,679,787,885,970,1039,1148,1235,1328,1421,1535,1615,1716,1787,1856,1937,2022,2112", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "124,242,324,414,503,595,674,782,880,965,1034,1143,1230,1323,1416,1530,1610,1711,1782,1851,1932,2017,2107,2201"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4257,4331,4449,4531,4621,4710,4802,4881,4989,5087,5172,5241,5350,5437,5530,5623,5737,5817,9989,10060,10129,10210,10295,10385", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "4326,4444,4526,4616,4705,4797,4876,4984,5082,5167,5236,5345,5432,5525,5618,5732,5812,5913,10055,10124,10205,10290,10380,10474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1373,1471,1539,1605,1670,1740,1871,2000,2136,2208,2289,2363,2451,2545,2636,2703,2769,2822,2883,2931,2992,3065,3141,3201,3271,3329,3386,3452,3504,3563,3639,3715,3770", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,51,58,75,75,54,66", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1368,1466,1534,1600,1665,1735,1866,1995,2131,2203,2284,2358,2446,2540,2631,2698,2764,2817,2878,2926,2987,3060,3136,3196,3266,3324,3381,3447,3499,3558,3634,3710,3765,3832"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,534,5918,6010,6100,6178,6268,6365,6452,6518,6615,6713,6781,6847,6912,6982,7113,7242,7378,7450,7531,7605,7693,7787,7878,7945,8699,8752,8813,8861,8922,8995,9071,9131,9201,9259,9316,9382,9434,9493,9569,9645,9700", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,51,58,75,75,54,66", "endOffsets": "330,529,721,6005,6095,6173,6263,6360,6447,6513,6610,6708,6776,6842,6907,6977,7108,7237,7373,7445,7526,7600,7688,7782,7873,7940,8006,8747,8808,8856,8917,8990,9066,9126,9196,9254,9311,9377,9429,9488,9564,9640,9695,9762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "726,837,946,1058,1143,1248,1365,1444,1522,1613,1706,1801,1895,1995,2088,2183,2278,2369,2460,2541,2647,2752,2850,2957,3060,3175,3336,10546", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "832,941,1053,1138,1243,1360,1439,1517,1608,1701,1796,1890,1990,2083,2178,2273,2364,2455,2536,2642,2747,2845,2952,3055,3170,3331,3433,10623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "46,47,48,49,50,51,52,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,3538,3640,3743,3850,3954,4058,10772", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3533,3635,3738,3845,3949,4053,4164,10868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8011,8082,8147,8218,8289,8376,8447,8534,8618", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "8077,8142,8213,8284,8371,8442,8529,8613,8694"}}]}]}