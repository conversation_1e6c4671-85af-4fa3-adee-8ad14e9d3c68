{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-c55e58a6ee0636a058f8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNPermissionsSpec_autolinked_build", "jsonFile": "directory-RNPermissionsSpec_autolinked_build-Debug-e604a14726fc41d639c3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-bccc452287eac0abfc00.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-147d8de20a97a1bec6d4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-9497695fe97baaff35e0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-39abfd1099299b2c5061.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "jsonFile": "target-react_codegen_RNPermissionsSpec-Debug-c736b6b26e853d9623cc.json", "name": "react_codegen_RNPermissionsSpec", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-e18a18f8c19e46e123ae.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-7c3e2e00664805c0169e.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-23233f69081dc9bc8730.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ab2da7e339cb318fabeb.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}