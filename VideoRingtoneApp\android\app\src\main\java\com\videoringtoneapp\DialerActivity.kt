package com.videoringtoneapp

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast

class DialerActivity : Activity() {

    companion object {
        private const val TAG = "DialerActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "DialerActivity created with intent: ${intent.action}")

        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "DialerActivity new intent: ${intent.action}")
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            Intent.ACTION_DIAL -> {
                val phoneNumber = intent.data?.schemeSpecificPart
                Log.d(TAG, "Dial intent received for number: $phoneNumber")

                if (phoneNumber != null) {
                    // For now, delegate to the system dialer for actual dialing
                    // In a full dialer app, you would show your own dialing interface
                    try {
                        val dialIntent = Intent(Intent.ACTION_CALL).apply {
                            data = Uri.parse("tel:$phoneNumber")
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        }
                        startActivity(dialIntent)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error making call", e)
                        Toast.makeText(this, "Error making call: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    // No number provided, open main app
                    openMainApp()
                }
            }
            "android.intent.action.CALL_PRIVILEGED" -> {
                val phoneNumber = intent.data?.schemeSpecificPart
                Log.d(TAG, "Privileged call intent received for number: $phoneNumber")

                if (phoneNumber != null) {
                    try {
                        val callIntent = Intent(Intent.ACTION_CALL).apply {
                            data = Uri.parse("tel:$phoneNumber")
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        }
                        startActivity(callIntent)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error making privileged call", e)
                        Toast.makeText(this, "Error making call: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    openMainApp()
                }
            }
            Intent.ACTION_VIEW -> {
                val phoneNumber = intent.data?.schemeSpecificPart
                Log.d(TAG, "View intent received for number: $phoneNumber")

                if (phoneNumber != null) {
                    // Show dialer with pre-filled number
                    try {
                        val dialIntent = Intent(Intent.ACTION_DIAL).apply {
                            data = Uri.parse("tel:$phoneNumber")
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        }
                        startActivity(dialIntent)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error opening dialer", e)
                        openMainApp()
                    }
                } else {
                    openMainApp()
                }
            }
            else -> {
                Log.d(TAG, "Unknown intent action: ${intent.action}")
                openMainApp()
            }
        }

        // Finish this activity since we're just a handler
        finish()
    }

    private fun openMainApp() {
        try {
            val mainIntent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            startActivity(mainIntent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening main app", e)
        }
    }
}
