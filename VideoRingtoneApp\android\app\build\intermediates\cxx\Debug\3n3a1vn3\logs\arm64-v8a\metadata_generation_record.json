[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a\\android_gradle_build.json due to:", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a file changed", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a\\build.ninja (LAST_MODIFIED_CHANGED)", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a\\compile_commands.json (LAST_MODIFIED_CHANGED)", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.12.7-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging4119202036072940541\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\8cb416b8178cb0e7f140959230f7f0bf\\\\transformed\\\\react-android-0.79.2-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\b91a94c10768de6e7e46fcdf99215bf3\\\\transformed\\\\hermes-android-0.79.2-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\72cde7dc85b5006383f56c98fcfedfa5\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "keeping json folder 'H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a' but regenerating project", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"H:\\\\Coding\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HH:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=H:\\\\Coding\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3n3a1vn3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3n3a1vn3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3n3a1vn3\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BH:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3n3a1vn3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"H:\\\\Coding\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HH:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=H:\\\\Coding\\\\android-sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=H:\\\\Coding\\\\android-sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3n3a1vn3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3n3a1vn3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3n3a1vn3\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BH:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\.cxx\\\\Debug\\\\3n3a1vn3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=H:\\\\Coding\\\\VibeCoding\\\\video-ringtone\\\\VideoRingtoneApp\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a\\compile_commands.json.bin normally", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3n3a1vn3\\obj\\arm64-v8a\\libc++_shared.so in incremental regenerate", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3n3a1vn3\\obj\\arm64-v8a\\libfbjni.so in incremental regenerate", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3n3a1vn3\\obj\\arm64-v8a\\libjsi.so in incremental regenerate", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\intermediates\\cxx\\Debug\\3n3a1vn3\\obj\\arm64-v8a\\libreactnative.so in incremental regenerate", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\Debug\\3n3a1vn3\\arm64-v8a\\compile_commands.json to H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]