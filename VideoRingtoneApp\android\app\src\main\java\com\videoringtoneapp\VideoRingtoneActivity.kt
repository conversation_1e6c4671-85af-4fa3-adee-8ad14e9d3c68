package com.videoringtoneapp

import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.telecom.TelecomManager
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.VideoView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import java.io.File

class VideoRingtoneActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "VideoRingtoneActivity"
        const val ACTION_FINISH_ACTIVITY = "FINISH_ACTIVITY"
        private const val VIDEO_RINGTONE_FILENAME = "video_ringtone.mp4"
        private const val PREFS_NAME = "video_ringtone_prefs"
        private const val PREF_VIDEO_PATH = "video_path"
    }

    private lateinit var videoView: VideoView
    private lateinit var callerNameText: TextView
    private lateinit var callerNumberText: TextView
    private lateinit var answerButton: Button
    private lateinit var declineButton: Button
    private lateinit var buttonContainer: LinearLayout
    private lateinit var callerInfoContainer: LinearLayout

    private var wakeLock: PowerManager.WakeLock? = null
    private var phoneNumber: String = "Unknown"
    private var audioManager: AudioManager? = null
    private var audioFocusRequest: AudioFocusRequest? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "Activity created")

        // Handle finish action
        if (intent.action == ACTION_FINISH_ACTIVITY) {
            finish()
            return
        }

        // Get phone number from intent
        phoneNumber = intent.getStringExtra("phone_number") ?: "Unknown"

        setupWindow()
        setupWakeLock()
        setupAudio()
        createUI()
        setupVideoPlayer()
    }

    private fun setupWindow() {
        // Show on lock screen and turn screen on
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        } else {
            @Suppress("DEPRECATION")
            window.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }

        // Make fullscreen
        window.addFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
        )

        // Hide system UI
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
    }

    private fun setupWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.FULL_WAKE_LOCK or
            PowerManager.ACQUIRE_CAUSES_WAKEUP or
            PowerManager.ON_AFTER_RELEASE,
            "VideoRingtone:ActivityWakeLock"
        )
        wakeLock?.acquire(30000) // 30 seconds max
    }

    private fun setupAudio() {
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager

        // Request audio focus for video ringtone
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build()

            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(false)
                .setWillPauseWhenDucked(false)
                .setOnAudioFocusChangeListener { focusChange ->
                    Log.d(TAG, "Audio focus changed: $focusChange")
                }
                .build()

            audioManager?.requestAudioFocus(audioFocusRequest!!)
        } else {
            @Suppress("DEPRECATION")
            audioManager?.requestAudioFocus(
                { focusChange -> Log.d(TAG, "Audio focus changed: $focusChange") },
                AudioManager.STREAM_RING,
                AudioManager.AUDIOFOCUS_GAIN
            )
        }

        Log.d(TAG, "Audio focus requested for video ringtone")
    }

    private fun createUI() {
        // Create main container with gradient background
        val mainContainer = RelativeLayout(this).apply {
            val gradient = GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                intArrayOf(Color.parseColor("#1a1a1a"), Color.parseColor("#000000"))
            )
            background = gradient
        }

        // Create video view (full screen background)
        videoView = VideoView(this).apply {
            layoutParams = RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT
            )
        }

        // Create overlay for caller info (top section)
        callerInfoContainer = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER_HORIZONTAL
            setPadding(40, 100, 40, 40)
            layoutParams = RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_TOP)
            }

            // Semi-transparent background
            val overlay = GradientDrawable().apply {
                setColor(Color.parseColor("#80000000"))
                cornerRadius = 20f
            }
            background = overlay
        }

        // Create "Incoming Call" label
        val incomingCallLabel = TextView(this).apply {
            text = "📞 Incoming Call"
            textSize = 18f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            typeface = Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 20)
        }

        // Create caller name
        callerNameText = TextView(this).apply {
            text = "Unknown Caller"
            textSize = 28f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            typeface = Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 10)
        }

        // Create caller number
        callerNumberText = TextView(this).apply {
            text = phoneNumber
            textSize = 20f
            setTextColor(Color.parseColor("#CCCCCC"))
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 20)
        }

        // Add views to caller info container
        callerInfoContainer.addView(incomingCallLabel)
        callerInfoContainer.addView(callerNameText)
        callerInfoContainer.addView(callerNumberText)

        // Create button container (bottom section)
        buttonContainer = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
            setPadding(60, 40, 60, 80)
            layoutParams = RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
            }
        }

        // Create decline button (red circle)
        declineButton = Button(this).apply {
            text = "✕"
            textSize = 24f
            setTextColor(Color.WHITE)
            typeface = Typeface.DEFAULT_BOLD

            val size = 120
            layoutParams = LinearLayout.LayoutParams(size, size).apply {
                setMargins(0, 0, 80, 0)
            }

            val drawable = GradientDrawable().apply {
                shape = GradientDrawable.OVAL
                setColor(Color.parseColor("#FF4444"))
                setStroke(4, Color.WHITE)
            }
            background = drawable

            setOnClickListener { declineCall() }
        }

        // Create answer button (green circle)
        answerButton = Button(this).apply {
            text = "✓"
            textSize = 24f
            setTextColor(Color.WHITE)
            typeface = Typeface.DEFAULT_BOLD

            val size = 120
            layoutParams = LinearLayout.LayoutParams(size, size).apply {
                setMargins(80, 0, 0, 0)
            }

            val drawable = GradientDrawable().apply {
                shape = GradientDrawable.OVAL
                setColor(Color.parseColor("#44AA44"))
                setStroke(4, Color.WHITE)
            }
            background = drawable

            setOnClickListener { answerCall() }
        }

        // Add buttons to container
        buttonContainer.addView(declineButton)
        buttonContainer.addView(answerButton)

        // Add all views to main container
        mainContainer.addView(videoView)
        mainContainer.addView(callerInfoContainer)
        mainContainer.addView(buttonContainer)

        setContentView(mainContainer)
    }

    private fun setupVideoPlayer() {
        try {
            // Use a default video or the one from assets
            val videoUri = getVideoUri()

            videoView.setVideoURI(videoUri)
            videoView.setOnPreparedListener { mediaPlayer ->
                mediaPlayer.isLooping = true
                mediaPlayer.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                videoView.start()
                Log.d(TAG, "Video started playing")
            }

            videoView.setOnErrorListener { _, what, extra ->
                Log.e(TAG, "Video error: what=$what, extra=$extra")
                // Hide video view on error
                videoView.visibility = View.GONE
                true
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error setting up video player", e)
            videoView.visibility = View.GONE
        }
    }

    private fun getVideoUri(): Uri {
        try {
            // Check if user has selected a custom video
            val videoFile = File(filesDir, "videos/$VIDEO_RINGTONE_FILENAME")
            if (videoFile.exists()) {
                Log.d(TAG, "Using custom video: ${videoFile.absolutePath}")
                return Uri.fromFile(videoFile)
            }

            // Check if there's a video in assets
            try {
                val assetFiles = assets.list("")
                val videoAssets = assetFiles?.filter { it.endsWith(".mp4") || it.endsWith(".3gp") || it.endsWith(".webm") }
                if (!videoAssets.isNullOrEmpty()) {
                    Log.d(TAG, "Using asset video: ${videoAssets[0]}")
                    return Uri.parse("android.asset:///${videoAssets[0]}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "No video assets found", e)
            }

            // Fallback to a default drawable (this won't play as video, but prevents crashes)
            Log.d(TAG, "Using fallback drawable")
            return Uri.parse("android.resource://$packageName/${android.R.drawable.ic_media_play}")

        } catch (e: Exception) {
            Log.e(TAG, "Error getting video URI", e)
            return Uri.parse("android.resource://$packageName/${android.R.drawable.ic_media_play}")
        }
    }

    private fun answerCall() {
        Log.d(TAG, "Answer button clicked")
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val telecomManager = getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                telecomManager.acceptRingingCall()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error answering call", e)
        }
        finishActivity()
    }

    private fun declineCall() {
        Log.d(TAG, "Decline button clicked")
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val telecomManager = getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                telecomManager.endCall()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error declining call", e)
        }
        finishActivity()
    }

    private fun finishActivity() {
        try {
            // Stop video
            if (::videoView.isInitialized) {
                videoView.stopPlayback()
            }

            // Release audio focus
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                audioFocusRequest?.let { request ->
                    audioManager?.abandonAudioFocusRequest(request)
                }
            } else {
                @Suppress("DEPRECATION")
                audioManager?.abandonAudioFocus { }
            }

            // Release wake lock
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                }
            }

            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error finishing activity", e)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (intent.action == ACTION_FINISH_ACTIVITY) {
            finishActivity()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Activity destroyed")
        finishActivity()
    }

    override fun onBackPressed() {
        // Prevent back button from closing the activity during incoming call
        // User must use answer/decline buttons
    }
}
