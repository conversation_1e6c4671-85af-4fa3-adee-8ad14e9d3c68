{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3517,3579,3655,3731,3789", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,52,61,75,75,57,69", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3512,3574,3650,3726,3784,3854"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,549,5888,5974,6062,6141,6239,6334,6411,6478,6578,6678,6744,6813,6880,6951,7082,7201,7327,7398,7484,7560,7637,7740,7845,7909,8671,8724,8782,8830,8891,8956,9026,9092,9164,9234,9302,9368,9421,9483,9559,9635,9693", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,52,61,75,75,57,69", "endOffsets": "336,544,727,5969,6057,6136,6234,6329,6406,6473,6573,6673,6739,6808,6875,6946,7077,7196,7322,7393,7479,7555,7632,7735,7840,7904,7968,8719,8777,8825,8886,8951,9021,9087,9159,9229,9297,9363,9416,9478,9554,9630,9688,9758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,836,940,1048,1133,1234,1362,1448,1529,1621,1715,1812,1906,2006,2100,2196,2291,2383,2475,2556,2664,2771,2878,2987,3092,3206,3383,10267", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "831,935,1043,1128,1229,1357,1443,1524,1616,1710,1807,1901,2001,2095,2191,2286,2378,2470,2551,2659,2766,2873,2982,3087,3201,3378,3477,10345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3482,3581,3683,3783,3881,3988,4094,10350", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3576,3678,3778,3876,3983,4089,4205,10446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,239,320,439,541,635,719,813,919,1021,1088,1193,1292,1389,1465,1557,1640,1733,1808,1880,1957,2034,2132", "endColumns": "74,108,80,118,101,93,83,93,105,101,66,104,98,96,75,91,82,92,74,71,76,76,97,104", "endOffsets": "125,234,315,434,536,630,714,808,914,1016,1083,1188,1287,1384,1460,1552,1635,1728,1803,1875,1952,2029,2127,2232"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4210,4285,4394,4475,4594,4696,4790,4874,4968,5074,5176,5243,5348,5447,5544,5620,5712,5795,9763,9838,9910,9987,10064,10162", "endColumns": "74,108,80,118,101,93,83,93,105,101,66,104,98,96,75,91,82,92,74,71,76,76,97,104", "endOffsets": "4280,4389,4470,4589,4691,4785,4869,4963,5069,5171,5238,5343,5442,5539,5615,5707,5790,5883,9833,9905,9982,10059,10157,10262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7973,8047,8110,8175,8254,8331,8407,8506,8602", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "8042,8105,8170,8249,8326,8402,8501,8597,8666"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3517,3579,3655,3731,3789", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,52,61,75,75,57,69", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3512,3574,3650,3726,3784,3854"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,549,5888,5974,6062,6141,6239,6334,6411,6478,6578,6678,6744,6813,6880,6951,7082,7201,7327,7398,7484,7560,7637,7740,7845,7909,8671,8724,8782,8830,8891,8956,9026,9092,9164,9234,9302,9368,9421,9483,9559,9635,9693", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,52,61,75,75,57,69", "endOffsets": "336,544,727,5969,6057,6136,6234,6329,6406,6473,6573,6673,6739,6808,6875,6946,7077,7196,7322,7393,7479,7555,7632,7735,7840,7904,7968,8719,8777,8825,8886,8951,9021,9087,9159,9229,9297,9363,9416,9478,9554,9630,9688,9758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,836,940,1048,1133,1234,1362,1448,1529,1621,1715,1812,1906,2006,2100,2196,2291,2383,2475,2556,2664,2771,2878,2987,3092,3206,3383,10267", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "831,935,1043,1128,1229,1357,1443,1524,1616,1710,1807,1901,2001,2095,2191,2286,2378,2470,2551,2659,2766,2873,2982,3087,3201,3378,3477,10345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3482,3581,3683,3783,3881,3988,4094,10350", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "3576,3678,3778,3876,3983,4089,4205,10446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,239,320,439,541,635,719,813,919,1021,1088,1193,1292,1389,1465,1557,1640,1733,1808,1880,1957,2034,2132", "endColumns": "74,108,80,118,101,93,83,93,105,101,66,104,98,96,75,91,82,92,74,71,76,76,97,104", "endOffsets": "125,234,315,434,536,630,714,808,914,1016,1083,1188,1287,1384,1460,1552,1635,1728,1803,1875,1952,2029,2127,2232"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4210,4285,4394,4475,4594,4696,4790,4874,4968,5074,5176,5243,5348,5447,5544,5620,5712,5795,9763,9838,9910,9987,10064,10162", "endColumns": "74,108,80,118,101,93,83,93,105,101,66,104,98,96,75,91,82,92,74,71,76,76,97,104", "endOffsets": "4280,4389,4470,4589,4691,4785,4869,4963,5069,5171,5238,5343,5442,5539,5615,5707,5790,5883,9833,9905,9982,10059,10157,10262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7973,8047,8110,8175,8254,8331,8407,8506,8602", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "8042,8105,8170,8249,8326,8402,8501,8597,8666"}}]}]}