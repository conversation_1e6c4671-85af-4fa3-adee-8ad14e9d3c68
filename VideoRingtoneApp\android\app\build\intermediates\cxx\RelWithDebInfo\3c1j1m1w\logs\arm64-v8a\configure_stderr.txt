CMake Warning in H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/./

  has 188 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/./

  has 188 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:
  The object file directory

    H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/./

  has 184 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    H_/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


