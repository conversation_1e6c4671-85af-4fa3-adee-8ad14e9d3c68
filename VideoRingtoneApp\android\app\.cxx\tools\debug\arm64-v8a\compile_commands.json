[{"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\H_\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\RNPermissionsSpec-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\RNPermissionsSpec-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\RNPermissionsSpec-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\1ac44fb29917a513567e7eceb3c4dfa7\\RNPermissionsSpecJSI-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\RNPermissionsSpecJSI-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\RNPermissionsSpecJSI-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\11362e57e5dca720d89d849811f909f0\\RNCSafeAreaViewShadowNode.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\982db2f69122ee290771c1a77076171b\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\21bd114425e66d2924540cc301e4613b\\safeareacontext\\ComponentDescriptors.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\21bd114425e66d2924540cc301e4613b\\safeareacontext\\EventEmitters.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\84810466fed9ae02a4d01bd98d9e41bc\\components\\safeareacontext\\Props.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\21bd114425e66d2924540cc301e4613b\\safeareacontext\\ShadowNodes.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\84810466fed9ae02a4d01bd98d9e41bc\\components\\safeareacontext\\States.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\03de314beaa0d70218a6841911723387\\safeareacontextJSI-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\fd6c6810e08ab9c131025d6d45a0ab59\\jni\\safeareacontext-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a7a5b6ef63fe8620daea19ac4f5e9acc\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a7a5b6ef63fe8620daea19ac4f5e9acc\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\cfc0f21988c7424a6729bc3fef3cc917\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\937f90bb1252508fb2a13819baaae3c0\\components\\rnscreens\\ComponentDescriptors.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d005d15700f84c5d28791fb8186f963b\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1b7494b9201882dd9ab653dea491eea7\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\e51082ca2170ab862755d4305da4d538\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1b7494b9201882dd9ab653dea491eea7\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\937f90bb1252508fb2a13819baaae3c0\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\796ad459e5c56493b6ccb0d610a9a963\\RNVectorIconsSpecJSI-generated.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "command": "H:\\Coding\\android-sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=H:/Coding/android-sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8cb416b8178cb0e7f140959230f7f0bf/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "H:\\Coding\\VibeCoding\\video-ringtone\\VideoRingtoneApp\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]