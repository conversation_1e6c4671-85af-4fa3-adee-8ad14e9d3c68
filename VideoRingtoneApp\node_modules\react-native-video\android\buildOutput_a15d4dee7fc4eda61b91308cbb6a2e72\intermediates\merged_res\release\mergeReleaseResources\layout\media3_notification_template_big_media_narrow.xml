<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2024 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Layout to be used with only max 3 actions. It has a much larger picture at the left side-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/status_bar_latest_event_content"
  android:layout_width="match_parent"
  android:layout_height="128dp"
  >
  <ImageView android:id="@+id/icon"
    android:layout_width="128dp"
    android:layout_height="128dp"
    android:scaleType="centerCrop"
    />

  <include layout="@layout/notification_media_cancel_action"
    android:layout_width="48dp"
    android:layout_height="48dp"
    android:layout_marginLeft="2dp"
    android:layout_marginRight="2dp"
    android:layout_alignParentRight="true"
    android:layout_alignParentEnd="true"/>

  <include layout="@layout/notification_template_lines_media"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="128dp"
    android:layout_marginStart="128dp"
    android:layout_toLeftOf="@id/cancel_action"
    android:layout_toStartOf="@id/cancel_action"/>

  <LinearLayout
    android:id="@+id/media_actions"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:layout_toRightOf="@id/icon"
    android:layout_toEndOf="@id/icon"
    android:layout_alignParentBottom="true"
    android:layout_marginLeft="12dp"
    android:layout_marginRight="12dp"
    android:orientation="horizontal"
    android:layoutDirection="ltr"
    >
    <!-- media buttons will be added here -->
  </LinearLayout>
  <ImageView
    android:layout_width="match_parent"
    android:layout_height="1dp"
    android:layout_toRightOf="@id/icon"
    android:layout_toEndOf="@id/icon"
    android:layout_above="@id/media_actions"
    android:id="@+id/action_divider"
    android:background="?android:attr/dividerHorizontal" />
</RelativeLayout>
