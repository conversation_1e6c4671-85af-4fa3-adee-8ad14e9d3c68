[{"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_open_exit.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_open_exit.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_close_exit.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_close_exit.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_fade_enter.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_fade_enter.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_open_enter.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_open_enter.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_close_enter.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_close_enter.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/animator/fragment_fade_exit.xml", "source": "com.brentvatne.react.react-native-video-fragment-1.5.4-0:/animator/fragment_fade_exit.xml"}]