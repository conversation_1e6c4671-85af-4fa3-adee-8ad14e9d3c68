<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="ltr"
    android:background="@color/player_overlay_color"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/live_wrapper_margin_top"
        android:id="@+id/exo_live_container">

        <ImageView
            android:id="@+id/exo_live_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/position_duration_horizontal_padding"
            android:src="@drawable/circle" />

        <TextView android:id="@+id/exo_live_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/position_duration_text_size"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:textColor="@color/white"/>
    </LinearLayout>
    
    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/controller_wrapper_padding_top"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <ImageButton android:id="@+id/exo_prev"
            style="@style/ExoMediaButton.Previous"/>

        <ImageButton android:id="@+id/exo_rew"
            style="@style/ExoMediaButton.Rewind"/>
        <FrameLayout
            android:id="@+id/exo_play_pause_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center">
            <ImageButton android:id="@+id/exo_play"
                style="@style/ExoMediaButton.Play"/>

            <ImageButton android:id="@+id/exo_pause"
                style="@style/ExoMediaButton.Pause"/>
        </FrameLayout>

        <ImageButton android:id="@+id/exo_ffwd"
            style="@style/ExoMediaButton.FastForward"/>

        <ImageButton android:id="@+id/exo_next"
            style="@style/ExoMediaButton.Next"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/seekBar_wrapper_margin_top"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView android:id="@+id/exo_position"
            android:layout_width="@dimen/position_duration_width"
            android:layout_height="wrap_content"
            android:textSize="@dimen/position_duration_text_size"
            android:textStyle="bold"
            android:paddingHorizontal="@dimen/position_duration_horizontal_padding"
            android:includeFontPadding="false"
            android:textColor="@color/silver_gray"/>

        <androidx.media3.ui.DefaultTimeBar
            android:id="@+id/exo_progress"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="@dimen/seekBar_height"/>

        <TextView android:id="@+id/exo_duration"
            android:layout_width="@dimen/position_duration_width"
            android:layout_height="wrap_content"
            android:textSize="@dimen/position_duration_text_size"
            android:textStyle="bold"
            android:paddingHorizontal="@dimen/position_duration_horizontal_padding"
            android:includeFontPadding="false"
            android:textColor="@color/silver_gray"/>

        <ImageButton
            android:id="@+id/exo_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/ExoStyledControls.Button.Bottom.Settings"
            />

        <ImageButton
            android:id="@+id/exo_fullscreen"
            style="@style/ExoMediaButton.FullScreen"
            android:layout_width="@dimen/full_screen_size"
            android:layout_height="@dimen/full_screen_size"
            android:layout_margin="@dimen/full_screen_margin"
            android:scaleType="fitCenter" />
    </LinearLayout>

</LinearLayout>
