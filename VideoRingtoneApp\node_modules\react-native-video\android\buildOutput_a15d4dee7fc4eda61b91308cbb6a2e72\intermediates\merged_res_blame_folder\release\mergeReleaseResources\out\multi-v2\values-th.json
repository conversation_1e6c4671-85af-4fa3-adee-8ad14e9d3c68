{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3114,3173,3239,3293,3356,3438,3515,3569", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3109,3168,3234,3288,3351,3433,3510,3564,3632"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,528,5764,5845,5924,6003,6091,6181,6252,6316,6407,6498,6562,6625,6690,6761,6870,6975,7088,7156,7239,7312,7383,7468,7551,7614,8359,8412,8470,8518,8579,8638,8706,8772,8840,8903,8962,9028,9082,9145,9227,9304,9358", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "328,523,701,5840,5919,5998,6086,6176,6247,6311,6402,6493,6557,6620,6685,6756,6865,6970,7083,7151,7234,7307,7378,7463,7546,7609,7673,8407,8465,8513,8574,8633,8701,8767,8835,8898,8957,9023,9077,9140,9222,9299,9353,9421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,220,295,394,493,581,660,748,840,928,1001,1099,1185,1279,1359,1459,1528,1616,1691,1757,1831,1914,2003", "endColumns": "71,92,74,98,98,87,78,87,91,87,72,97,85,93,79,99,68,87,74,65,73,82,88,95", "endOffsets": "122,215,290,389,488,576,655,743,835,923,996,1094,1180,1274,1354,1454,1523,1611,1686,1752,1826,1909,1998,2094"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,4275,4368,4443,4542,4641,4729,4808,4896,4988,5076,5149,5247,5333,5427,5507,5607,5676,9718,9793,9859,9933,10016,10105", "endColumns": "71,92,74,98,98,87,78,87,91,87,72,97,85,93,79,99,68,87,74,65,73,82,88,95", "endOffsets": "4270,4363,4438,4537,4636,4724,4803,4891,4983,5071,5144,5242,5328,5422,5502,5602,5671,5759,9788,9854,9928,10011,10100,10196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7678,7754,7817,7885,7953,8030,8103,8194,8280", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "7749,7812,7880,7948,8025,8098,8189,8275,8354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,208,280,350,432,500,567,640,719,803,889,958,1035,1116,1192,1273,1354,1430,1505,1580,1666,1736,1812,1886", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "126,203,275,345,427,495,562,635,714,798,884,953,1030,1111,1187,1268,1349,1425,1500,1575,1661,1731,1807,1881,1960"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3335,4126,9426,9498,9568,9650,10201,10268,10341,10420,10504,10590,10659,10818,10899,10975,11056,11137,11213,11288,11363,11550,11620,11696,11770", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "3406,4198,9493,9563,9645,9713,10263,10336,10415,10499,10585,10654,10731,10894,10970,11051,11132,11208,11283,11358,11444,11615,11691,11765,11844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,811,904,1012,1097,1199,1309,1387,1464,1555,1648,1739,1833,1933,2026,2121,2215,2306,2397,2478,2581,2679,2777,2880,2986,3087,3240,10736", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "806,899,1007,1092,1194,1304,1382,1459,1550,1643,1734,1828,1928,2021,2116,2210,2301,2392,2473,2576,2674,2772,2875,2981,3082,3235,3330,10813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3411,3507,3610,3708,3806,3909,4014,11449", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3502,3605,3703,3801,3904,4009,4121,11545"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3114,3173,3239,3293,3356,3438,3515,3569", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3109,3168,3234,3288,3351,3433,3510,3564,3632"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,528,5764,5845,5924,6003,6091,6181,6252,6316,6407,6498,6562,6625,6690,6761,6870,6975,7088,7156,7239,7312,7383,7468,7551,7614,8359,8412,8470,8518,8579,8638,8706,8772,8840,8903,8962,9028,9082,9145,9227,9304,9358", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,53,62,81,76,53,67", "endOffsets": "328,523,701,5840,5919,5998,6086,6176,6247,6311,6402,6493,6557,6620,6685,6756,6865,6970,7083,7151,7234,7307,7378,7463,7546,7609,7673,8407,8465,8513,8574,8633,8701,8767,8835,8898,8957,9023,9077,9140,9222,9299,9353,9421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,220,295,394,493,581,660,748,840,928,1001,1099,1185,1279,1359,1459,1528,1616,1691,1757,1831,1914,2003", "endColumns": "71,92,74,98,98,87,78,87,91,87,72,97,85,93,79,99,68,87,74,65,73,82,88,95", "endOffsets": "122,215,290,389,488,576,655,743,835,923,996,1094,1180,1274,1354,1454,1523,1611,1686,1752,1826,1909,1998,2094"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,4275,4368,4443,4542,4641,4729,4808,4896,4988,5076,5149,5247,5333,5427,5507,5607,5676,9718,9793,9859,9933,10016,10105", "endColumns": "71,92,74,98,98,87,78,87,91,87,72,97,85,93,79,99,68,87,74,65,73,82,88,95", "endOffsets": "4270,4363,4438,4537,4636,4724,4803,4891,4983,5071,5144,5242,5328,5422,5502,5602,5671,5759,9788,9854,9928,10011,10100,10196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7678,7754,7817,7885,7953,8030,8103,8194,8280", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "7749,7812,7880,7948,8025,8098,8189,8275,8354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,208,280,350,432,500,567,640,719,803,889,958,1035,1116,1192,1273,1354,1430,1505,1580,1666,1736,1812,1886", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "126,203,275,345,427,495,562,635,714,798,884,953,1030,1111,1187,1268,1349,1425,1500,1575,1661,1731,1807,1881,1960"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3335,4126,9426,9498,9568,9650,10201,10268,10341,10420,10504,10590,10659,10818,10899,10975,11056,11137,11213,11288,11363,11550,11620,11696,11770", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "3406,4198,9493,9563,9645,9713,10263,10336,10415,10499,10585,10654,10731,10894,10970,11051,11132,11208,11283,11358,11444,11615,11691,11765,11844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,811,904,1012,1097,1199,1309,1387,1464,1555,1648,1739,1833,1933,2026,2121,2215,2306,2397,2478,2581,2679,2777,2880,2986,3087,3240,10736", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "806,899,1007,1092,1194,1304,1382,1459,1550,1643,1734,1828,1928,2021,2116,2210,2301,2392,2473,2576,2674,2772,2875,2981,3082,3235,3330,10813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3411,3507,3610,3708,3806,3909,4014,11449", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3502,3605,3703,3801,3904,4009,4121,11545"}}]}]}