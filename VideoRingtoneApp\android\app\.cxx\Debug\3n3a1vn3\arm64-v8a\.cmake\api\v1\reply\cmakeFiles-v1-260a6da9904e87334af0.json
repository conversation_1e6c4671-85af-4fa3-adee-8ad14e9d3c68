{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isGenerated": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "H:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a", "source": "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 1, "minor": 0}}