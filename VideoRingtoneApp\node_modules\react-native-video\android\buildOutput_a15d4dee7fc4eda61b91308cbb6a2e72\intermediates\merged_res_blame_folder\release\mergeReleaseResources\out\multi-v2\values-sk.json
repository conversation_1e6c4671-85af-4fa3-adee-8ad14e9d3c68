{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3736,3800,3878,3956,4010", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3731,3795,3873,3951,4005,4071"}, "to": {"startLines": "2,11,17,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,6188,6269,6349,6431,6534,6633,6712,6777,6868,6962,7032,7098,7163,7240,7362,7479,7600,7674,7756,7829,7911,8011,8110,8177,8913,8966,9024,9072,9133,9205,9279,9342,9415,9480,9540,9605,9657,9721,9799,9877,9931", "endLines": "10,16,22,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "338,669,983,6264,6344,6426,6529,6628,6707,6772,6863,6957,7027,7093,7158,7235,7357,7474,7595,7669,7751,7824,7906,8006,8105,8172,8237,8961,9019,9067,9128,9200,9274,9337,9410,9475,9535,9600,9652,9716,9794,9872,9926,9992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1095,1196,1307,1393,1501,1619,1698,1775,1866,1959,2057,2151,2251,2344,2439,2537,2628,2719,2803,2908,3016,3115,3221,3333,3436,3602,11386", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1090,1191,1302,1388,1496,1614,1693,1770,1861,1954,2052,2146,2246,2339,2434,2532,2623,2714,2798,2903,3011,3110,3216,3328,3431,3597,3695,11464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "51,52,53,54,55,56,57,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3775,3871,3973,4074,4172,4282,4390,12104", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3866,3968,4069,4167,4277,4385,4507,12200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,240,316,420,513,591,668,758,856,934,1006,1106,1189,1283,1366,1462,1542,1648,1721,1790,1877,1968,2061", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "124,235,311,415,508,586,663,753,851,929,1001,1101,1184,1278,1361,1457,1537,1643,1716,1785,1872,1963,2056,2162"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4595,4669,4780,4856,4960,5053,5131,5208,5298,5396,5474,5546,5646,5729,5823,5906,6002,6082,10294,10367,10436,10523,10614,10707", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "4664,4775,4851,4955,5048,5126,5203,5293,5391,5469,5541,5641,5724,5818,5901,5997,6077,6183,10362,10431,10518,10609,10702,10808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8242,8319,8381,8445,8516,8593,8667,8751,8833", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "8314,8376,8440,8511,8588,8662,8746,8828,8908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "50,58,127,128,129,130,137,138,139,140,141,142,143,145,146,147,148,149,150,151,152,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3700,4512,9997,10068,10139,10226,10813,10882,10963,11044,11131,11226,11300,11469,11553,11630,11711,11793,11871,11946,12020,12205,12276,12355,12426", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3770,4590,10063,10134,10221,10289,10877,10958,11039,11126,11221,11295,11381,11548,11625,11706,11788,11866,11941,12015,12099,12271,12350,12421,12504"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3736,3800,3878,3956,4010", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3731,3795,3873,3951,4005,4071"}, "to": {"startLines": "2,11,17,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,6188,6269,6349,6431,6534,6633,6712,6777,6868,6962,7032,7098,7163,7240,7362,7479,7600,7674,7756,7829,7911,8011,8110,8177,8913,8966,9024,9072,9133,9205,9279,9342,9415,9480,9540,9605,9657,9721,9799,9877,9931", "endLines": "10,16,22,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "338,669,983,6264,6344,6426,6529,6628,6707,6772,6863,6957,7027,7093,7158,7235,7357,7474,7595,7669,7751,7824,7906,8006,8105,8172,8237,8961,9019,9067,9128,9200,9274,9337,9410,9475,9535,9600,9652,9716,9794,9872,9926,9992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1095,1196,1307,1393,1501,1619,1698,1775,1866,1959,2057,2151,2251,2344,2439,2537,2628,2719,2803,2908,3016,3115,3221,3333,3436,3602,11386", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1090,1191,1302,1388,1496,1614,1693,1770,1861,1954,2052,2146,2246,2339,2434,2532,2623,2714,2798,2903,3011,3110,3216,3328,3431,3597,3695,11464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "51,52,53,54,55,56,57,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3775,3871,3973,4074,4172,4282,4390,12104", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3866,3968,4069,4167,4277,4385,4507,12200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,240,316,420,513,591,668,758,856,934,1006,1106,1189,1283,1366,1462,1542,1648,1721,1790,1877,1968,2061", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "124,235,311,415,508,586,663,753,851,929,1001,1101,1184,1278,1361,1457,1537,1643,1716,1785,1872,1963,2056,2162"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4595,4669,4780,4856,4960,5053,5131,5208,5298,5396,5474,5546,5646,5729,5823,5906,6002,6082,10294,10367,10436,10523,10614,10707", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "4664,4775,4851,4955,5048,5126,5203,5293,5391,5469,5541,5641,5724,5818,5901,5997,6077,6183,10362,10431,10518,10609,10702,10808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8242,8319,8381,8445,8516,8593,8667,8751,8833", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "8314,8376,8440,8511,8588,8662,8746,8828,8908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "50,58,127,128,129,130,137,138,139,140,141,142,143,145,146,147,148,149,150,151,152,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3700,4512,9997,10068,10139,10226,10813,10882,10963,11044,11131,11226,11300,11469,11553,11630,11711,11793,11871,11946,12020,12205,12276,12355,12426", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3770,4590,10063,10134,10221,10289,10877,10958,11039,11126,11221,11295,11381,11548,11625,11706,11788,11866,11941,12015,12099,12271,12350,12421,12504"}}]}]}