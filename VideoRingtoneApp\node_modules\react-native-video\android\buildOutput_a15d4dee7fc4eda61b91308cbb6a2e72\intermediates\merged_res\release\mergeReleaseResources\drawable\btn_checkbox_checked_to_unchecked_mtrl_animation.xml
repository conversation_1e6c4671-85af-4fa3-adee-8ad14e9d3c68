<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<animated-vector
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:drawable="@drawable/btn_checkbox_checked_mtrl"
        tools:ignore="NewApi">
    <target
            android:name="icon_null"
            android:animation="@anim/btn_checkbox_to_unchecked_icon_null_animation" />
    <target
            android:name="check_path_merged"
            android:animation="@anim/btn_checkbox_to_unchecked_check_path_merged_animation" />
    <target
            android:name="box_inner_merged"
            android:animation="@anim/btn_checkbox_to_unchecked_box_inner_merged_animation" />
</animated-vector>
