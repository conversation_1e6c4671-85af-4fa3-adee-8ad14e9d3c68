{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7814,7891,7950,8015,8076,8156,8228,8318,8414", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "7886,7945,8010,8071,8151,8223,8313,8409,8485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3394,4176,9538,9609,9679,9762,10302,10369,10446,10526,10611,10691,10761,10924,11009,11084,11169,11255,11332,11406,11477,11665,11735,11814,11889", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "3458,4248,9604,9674,9757,9828,10364,10441,10521,10606,10686,10756,10839,11004,11079,11164,11250,11327,11401,11472,11559,11730,11809,11884,11961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,241,315,417,511,603,695,792,880,971,1035,1130,1227,1318,1396,1486,1559,1649,1720,1787,1862,1941,2028", "endColumns": "79,105,73,101,93,91,91,96,87,90,63,94,96,90,77,89,72,89,70,66,74,78,86,89", "endOffsets": "130,236,310,412,506,598,690,787,875,966,1030,1125,1222,1313,1391,1481,1554,1644,1715,1782,1857,1936,2023,2113"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4253,4333,4439,4513,4615,4709,4801,4893,4990,5078,5169,5233,5328,5425,5516,5594,5684,5757,9833,9904,9971,10046,10125,10212", "endColumns": "79,105,73,101,93,91,91,96,87,90,63,94,96,90,77,89,72,89,70,66,74,78,86,89", "endOffsets": "4328,4434,4508,4610,4704,4796,4888,4985,5073,5164,5228,5323,5420,5511,5589,5679,5752,5842,9899,9966,10041,10120,10207,10297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3333,3393,3467,3541,3598", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3328,3388,3462,3536,3593,3662"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,5847,5936,6029,6104,6189,6275,6350,6416,6501,6587,6655,6717,6777,6846,6963,7075,7192,7261,7345,7415,7491,7586,7685,7750,8490,8543,8601,8649,8710,8774,8841,8903,8969,9031,9088,9152,9204,9264,9338,9412,9469", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "330,517,697,5931,6024,6099,6184,6270,6345,6411,6496,6582,6650,6712,6772,6841,6958,7070,7187,7256,7340,7410,7486,7581,7680,7745,7809,8538,8596,8644,8705,8769,8836,8898,8964,9026,9083,9147,9199,9259,9333,9407,9464,9533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3463,3560,3662,3760,3857,3959,4065,11564", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3555,3657,3755,3852,3954,4060,4171,11660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "702,816,915,1027,1112,1218,1338,1418,1493,1584,1677,1769,1863,1963,2056,2158,2253,2344,2435,2514,2621,2725,2821,2928,3031,3140,3296,10844", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "811,910,1022,1107,1213,1333,1413,1488,1579,1672,1764,1858,1958,2051,2153,2248,2339,2430,2509,2616,2720,2816,2923,3026,3135,3291,3389,10919"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7814,7891,7950,8015,8076,8156,8228,8318,8414", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "7886,7945,8010,8071,8151,8223,8313,8409,8485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3394,4176,9538,9609,9679,9762,10302,10369,10446,10526,10611,10691,10761,10924,11009,11084,11169,11255,11332,11406,11477,11665,11735,11814,11889", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "3458,4248,9604,9674,9757,9828,10364,10441,10521,10606,10686,10756,10839,11004,11079,11164,11250,11327,11401,11472,11559,11730,11809,11884,11961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,241,315,417,511,603,695,792,880,971,1035,1130,1227,1318,1396,1486,1559,1649,1720,1787,1862,1941,2028", "endColumns": "79,105,73,101,93,91,91,96,87,90,63,94,96,90,77,89,72,89,70,66,74,78,86,89", "endOffsets": "130,236,310,412,506,598,690,787,875,966,1030,1125,1222,1313,1391,1481,1554,1644,1715,1782,1857,1936,2023,2113"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4253,4333,4439,4513,4615,4709,4801,4893,4990,5078,5169,5233,5328,5425,5516,5594,5684,5757,9833,9904,9971,10046,10125,10212", "endColumns": "79,105,73,101,93,91,91,96,87,90,63,94,96,90,77,89,72,89,70,66,74,78,86,89", "endOffsets": "4328,4434,4508,4610,4704,4796,4888,4985,5073,5164,5228,5323,5420,5511,5589,5679,5752,5842,9899,9966,10041,10120,10207,10297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3333,3393,3467,3541,3598", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3328,3388,3462,3536,3593,3662"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,522,5847,5936,6029,6104,6189,6275,6350,6416,6501,6587,6655,6717,6777,6846,6963,7075,7192,7261,7345,7415,7491,7586,7685,7750,8490,8543,8601,8649,8710,8774,8841,8903,8969,9031,9088,9152,9204,9264,9338,9412,9469", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "330,517,697,5931,6024,6099,6184,6270,6345,6411,6496,6582,6650,6712,6772,6841,6958,7070,7187,7256,7340,7410,7486,7581,7680,7745,7809,8538,8596,8644,8705,8769,8836,8898,8964,9026,9083,9147,9199,9259,9333,9407,9464,9533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3463,3560,3662,3760,3857,3959,4065,11564", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3555,3657,3755,3852,3954,4060,4171,11660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "702,816,915,1027,1112,1218,1338,1418,1493,1584,1677,1769,1863,1963,2056,2158,2253,2344,2435,2514,2621,2725,2821,2928,3031,3140,3296,10844", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "811,910,1022,1107,1213,1333,1413,1488,1579,1672,1764,1858,1958,2051,2153,2248,2339,2430,2509,2616,2720,2816,2923,3026,3135,3291,3389,10919"}}]}]}