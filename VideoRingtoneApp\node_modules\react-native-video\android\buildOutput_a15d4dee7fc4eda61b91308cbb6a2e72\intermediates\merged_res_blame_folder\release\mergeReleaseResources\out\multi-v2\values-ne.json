{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,237,307,404,498,582,664,754,857,946,1020,1121,1208,1305,1378,1475,1556,1656,1731,1807,1888,1972,2064", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "131,232,302,399,493,577,659,749,852,941,1015,1116,1203,1300,1373,1470,1551,1651,1726,1802,1883,1967,2059,2157"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4345,4426,4527,4597,4694,4788,4872,4954,5044,5147,5236,5310,5411,5498,5595,5668,5765,5846,9991,10066,10142,10223,10307,10399", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "4421,4522,4592,4689,4783,4867,4949,5039,5142,5231,5305,5406,5493,5590,5663,5760,5841,5941,10061,10137,10218,10302,10394,10492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,890,1001,1109,1200,1307,1427,1511,1590,1681,1774,1869,1963,2063,2156,2251,2345,2436,2527,2613,2726,2827,2930,3043,3153,3270,3437,10564", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "885,996,1104,1195,1302,1422,1506,1585,1676,1769,1864,1958,2058,2151,2246,2340,2431,2522,2608,2721,2822,2925,3038,3148,3265,3432,3543,10639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "53,122,123,124,131,133,134", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4268,9776,9844,9923,10497,10644,10714", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4340,9839,9918,9986,10559,10709,10778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8024,8095,8166,8236,8303,8381,8458,8558,8652", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "8090,8161,8231,8298,8376,8453,8553,8647,8716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "46,47,48,49,50,51,52,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3548,3651,3754,3856,3962,4060,4160,10783", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3646,3749,3851,3957,4055,4155,4263,10879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1458,1555,1627,1700,1760,1830,1947,2061,2181,2260,2352,2420,2506,2592,2677,2746,2809,2862,2920,2968,3029,3091,3162,3224,3286,3345,3412,3478,3532,3594,3670,3746,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1453,1550,1622,1695,1755,1825,1942,2056,2176,2255,2347,2415,2501,2587,2672,2741,2804,2857,2915,2963,3024,3086,3157,3219,3281,3340,3407,3473,3527,3589,3665,3741,3794,3859"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,360,579,5946,6039,6132,6222,6323,6426,6512,6576,6673,6770,6842,6915,6975,7045,7162,7276,7396,7475,7567,7635,7721,7807,7892,7961,8721,8774,8832,8880,8941,9003,9074,9136,9198,9257,9324,9390,9444,9506,9582,9658,9711", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "355,574,776,6034,6127,6217,6318,6421,6507,6571,6668,6765,6837,6910,6970,7040,7157,7271,7391,7470,7562,7630,7716,7802,7887,7956,8019,8769,8827,8875,8936,8998,9069,9131,9193,9252,9319,9385,9439,9501,9577,9653,9706,9771"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,237,307,404,498,582,664,754,857,946,1020,1121,1208,1305,1378,1475,1556,1656,1731,1807,1888,1972,2064", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "131,232,302,399,493,577,659,749,852,941,1015,1116,1203,1300,1373,1470,1551,1651,1726,1802,1883,1967,2059,2157"}, "to": {"startLines": "54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4345,4426,4527,4597,4694,4788,4872,4954,5044,5147,5236,5310,5411,5498,5595,5668,5765,5846,9991,10066,10142,10223,10307,10399", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "4421,4522,4592,4689,4783,4867,4949,5039,5142,5231,5305,5406,5493,5590,5663,5760,5841,5941,10061,10137,10218,10302,10394,10492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,890,1001,1109,1200,1307,1427,1511,1590,1681,1774,1869,1963,2063,2156,2251,2345,2436,2527,2613,2726,2827,2930,3043,3153,3270,3437,10564", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "885,996,1104,1195,1302,1422,1506,1585,1676,1769,1864,1958,2058,2151,2246,2340,2431,2522,2608,2721,2822,2925,3038,3148,3265,3432,3543,10639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "53,122,123,124,131,133,134", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4268,9776,9844,9923,10497,10644,10714", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4340,9839,9918,9986,10559,10709,10778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8024,8095,8166,8236,8303,8381,8458,8558,8652", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "8090,8161,8231,8298,8376,8453,8553,8647,8716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "46,47,48,49,50,51,52,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3548,3651,3754,3856,3962,4060,4160,10783", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3646,3749,3851,3957,4055,4155,4263,10879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1458,1555,1627,1700,1760,1830,1947,2061,2181,2260,2352,2420,2506,2592,2677,2746,2809,2862,2920,2968,3029,3091,3162,3224,3286,3345,3412,3478,3532,3594,3670,3746,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1453,1550,1622,1695,1755,1825,1942,2056,2176,2255,2347,2415,2501,2587,2672,2741,2804,2857,2915,2963,3024,3086,3157,3219,3281,3340,3407,3473,3527,3589,3665,3741,3794,3859"}, "to": {"startLines": "2,11,15,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,360,579,5946,6039,6132,6222,6323,6426,6512,6576,6673,6770,6842,6915,6975,7045,7162,7276,7396,7475,7567,7635,7721,7807,7892,7961,8721,8774,8832,8880,8941,9003,9074,9136,9198,9257,9324,9390,9444,9506,9582,9658,9711", "endLines": "10,14,18,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "355,574,776,6034,6127,6217,6318,6421,6507,6571,6668,6765,6837,6910,6970,7040,7157,7271,7391,7470,7562,7630,7716,7802,7887,7956,8019,8769,8827,8875,8936,8998,9069,9131,9193,9252,9319,9385,9439,9501,9577,9653,9706,9771"}}]}]}