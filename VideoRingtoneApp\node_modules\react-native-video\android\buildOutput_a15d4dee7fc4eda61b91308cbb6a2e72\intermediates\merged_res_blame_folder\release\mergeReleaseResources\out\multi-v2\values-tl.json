{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,240,313,418,517,598,677,772,858,941,1008,1114,1204,1305,1392,1490,1571,1666,1736,1804,1887,1974,2071", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "130,235,308,413,512,593,672,767,853,936,1003,1109,1199,1300,1387,1485,1566,1661,1731,1799,1882,1969,2066,2166"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4216,4296,4401,4474,4579,4678,4759,4838,4933,5019,5102,5169,5275,5365,5466,5553,5651,5732,9632,9702,9770,9853,9940,10037", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "4291,4396,4469,4574,4673,4754,4833,4928,5014,5097,5164,5270,5360,5461,5548,5646,5727,5822,9697,9765,9848,9935,10032,10132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7857,7934,7998,8066,8132,8212,8290,8390,8488", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "7929,7993,8061,8127,8207,8285,8385,8483,8561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3482,3579,3681,3782,3879,3986,4094,10222", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3574,3676,3777,3874,3981,4089,4211,10318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3442,3504,3583,3662,3719", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3437,3499,3578,3657,3714,3783"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,547,5827,5918,6009,6089,6174,6265,6343,6409,6510,6613,6680,6745,6807,6878,6996,7116,7236,7305,7392,7466,7546,7637,7728,7793,8566,8619,8677,8725,8786,8851,8913,8978,9046,9110,9168,9234,9286,9348,9427,9506,9563", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "330,542,737,5913,6004,6084,6169,6260,6338,6404,6505,6608,6675,6740,6802,6873,6991,7111,7231,7300,7387,7461,7541,7632,7723,7788,7852,8614,8672,8720,8781,8846,8908,8973,9041,9105,9163,9229,9281,9343,9422,9501,9558,9627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,853,961,1074,1162,1268,1383,1463,1540,1631,1724,1819,1913,2013,2106,2201,2295,2386,2477,2561,2670,2780,2881,2991,3109,3217,3380,10137", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "848,956,1069,1157,1263,1378,1458,1535,1626,1719,1814,1908,2008,2101,2196,2290,2381,2472,2556,2665,2775,2876,2986,3104,3212,3375,3477,10217"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,240,313,418,517,598,677,772,858,941,1008,1114,1204,1305,1392,1490,1571,1666,1736,1804,1887,1974,2071", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "130,235,308,413,512,593,672,767,853,936,1003,1109,1199,1300,1387,1485,1566,1661,1731,1799,1882,1969,2066,2166"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4216,4296,4401,4474,4579,4678,4759,4838,4933,5019,5102,5169,5275,5365,5466,5553,5651,5732,9632,9702,9770,9853,9940,10037", "endColumns": "79,104,72,104,98,80,78,94,85,82,66,105,89,100,86,97,80,94,69,67,82,86,96,99", "endOffsets": "4291,4396,4469,4574,4673,4754,4833,4928,5014,5097,5164,5270,5360,5461,5548,5646,5727,5822,9697,9765,9848,9935,10032,10132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7857,7934,7998,8066,8132,8212,8290,8390,8488", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "7929,7993,8061,8127,8207,8285,8385,8483,8561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3482,3579,3681,3782,3879,3986,4094,10222", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3574,3676,3777,3874,3981,4089,4211,10318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3442,3504,3583,3662,3719", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3437,3499,3578,3657,3714,3783"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,547,5827,5918,6009,6089,6174,6265,6343,6409,6510,6613,6680,6745,6807,6878,6996,7116,7236,7305,7392,7466,7546,7637,7728,7793,8566,8619,8677,8725,8786,8851,8913,8978,9046,9110,9168,9234,9286,9348,9427,9506,9563", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,51,61,78,78,56,68", "endOffsets": "330,542,737,5913,6004,6084,6169,6260,6338,6404,6505,6608,6675,6740,6802,6873,6991,7111,7231,7300,7387,7461,7541,7632,7723,7788,7852,8614,8672,8720,8781,8846,8908,8973,9041,9105,9163,9229,9281,9343,9422,9501,9558,9627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,853,961,1074,1162,1268,1383,1463,1540,1631,1724,1819,1913,2013,2106,2201,2295,2386,2477,2561,2670,2780,2881,2991,3109,3217,3380,10137", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "848,956,1069,1157,1263,1378,1458,1535,1626,1719,1814,1908,2008,2101,2196,2290,2381,2472,2556,2665,2775,2876,2986,3104,3212,3375,3477,10217"}}]}]}