{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3377,3437,3511,3585,3638", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3372,3432,3506,3580,3633,3708"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,543,6004,6087,6168,6251,6346,6446,6515,6578,6664,6750,6815,6879,6943,7011,7124,7240,7352,7425,7509,7578,7647,7731,7813,7880,8711,8764,8826,8880,8941,9001,9068,9131,9201,9262,9324,9390,9450,9510,9584,9658,9711", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "331,538,744,6082,6163,6246,6341,6441,6510,6573,6659,6745,6810,6874,6938,7006,7119,7235,7347,7420,7504,7573,7642,7726,7808,7875,7938,8759,8821,8875,8936,8996,9063,9126,9196,9257,9319,9385,9445,9505,9579,9653,9706,9781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3500,3598,3701,3806,3907,4020,4126,11788", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3593,3696,3801,3902,4015,4121,4248,11884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,246,316,424,523,623,705,793,896,986,1055,1157,1254,1353,1445,1547,1625,1727,1795,1862,1938,2017,2104", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "126,241,311,419,518,618,700,788,891,981,1050,1152,1249,1348,1440,1542,1620,1722,1790,1857,1933,2012,2099,2191"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4332,4408,4523,4593,4701,4800,4900,4982,5070,5173,5263,5332,5434,5531,5630,5722,5824,5902,10073,10141,10208,10284,10363,10450", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "4403,4518,4588,4696,4795,4895,4977,5065,5168,5258,5327,5429,5526,5625,5717,5819,5897,5999,10136,10203,10279,10358,10445,10537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "749,855,953,1063,1149,1251,1372,1450,1527,1618,1711,1806,1900,2000,2093,2188,2282,2373,2464,2545,2650,2752,2850,2960,3063,3172,3330,11070", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "850,948,1058,1144,1246,1367,1445,1522,1613,1706,1801,1895,1995,2088,2183,2277,2368,2459,2540,2645,2747,2845,2955,3058,3167,3325,3426,11147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7943,8011,8077,8148,8216,8312,8380,8503,8624", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "8006,8072,8143,8211,8307,8375,8498,8619,8706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,343,423,490,557,631,707,790,871,939,1018,1096,1171,1258,1344,1419,1493,1567,1654,1726,1801,1870", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "119,198,269,338,418,485,552,626,702,785,866,934,1013,1091,1166,1253,1339,1414,1488,1562,1649,1721,1796,1865,1938"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3431,4253,9786,9857,9926,10006,10542,10609,10683,10759,10842,10923,10991,11152,11230,11305,11392,11478,11553,11627,11701,11889,11961,12036,12105", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "3495,4327,9852,9921,10001,10068,10604,10678,10754,10837,10918,10986,11065,11225,11300,11387,11473,11548,11622,11696,11783,11956,12031,12100,12173"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3377,3437,3511,3585,3638", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3372,3432,3506,3580,3633,3708"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,543,6004,6087,6168,6251,6346,6446,6515,6578,6664,6750,6815,6879,6943,7011,7124,7240,7352,7425,7509,7578,7647,7731,7813,7880,8711,8764,8826,8880,8941,9001,9068,9131,9201,9262,9324,9390,9450,9510,9584,9658,9711", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "331,538,744,6082,6163,6246,6341,6441,6510,6573,6659,6745,6810,6874,6938,7006,7119,7235,7347,7420,7504,7573,7642,7726,7808,7875,7938,8759,8821,8875,8936,8996,9063,9126,9196,9257,9319,9385,9445,9505,9579,9653,9706,9781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3500,3598,3701,3806,3907,4020,4126,11788", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3593,3696,3801,3902,4015,4121,4248,11884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,246,316,424,523,623,705,793,896,986,1055,1157,1254,1353,1445,1547,1625,1727,1795,1862,1938,2017,2104", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "126,241,311,419,518,618,700,788,891,981,1050,1152,1249,1348,1440,1542,1620,1722,1790,1857,1933,2012,2099,2191"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4332,4408,4523,4593,4701,4800,4900,4982,5070,5173,5263,5332,5434,5531,5630,5722,5824,5902,10073,10141,10208,10284,10363,10450", "endColumns": "75,114,69,107,98,99,81,87,102,89,68,101,96,98,91,101,77,101,67,66,75,78,86,91", "endOffsets": "4403,4518,4588,4696,4795,4895,4977,5065,5168,5258,5327,5429,5526,5625,5717,5819,5897,5999,10136,10203,10279,10358,10445,10537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "749,855,953,1063,1149,1251,1372,1450,1527,1618,1711,1806,1900,2000,2093,2188,2282,2373,2464,2545,2650,2752,2850,2960,3063,3172,3330,11070", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "850,948,1058,1144,1246,1367,1445,1522,1613,1706,1801,1895,1995,2088,2183,2277,2368,2459,2540,2645,2747,2845,2955,3058,3167,3325,3426,11147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7943,8011,8077,8148,8216,8312,8380,8503,8624", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "8006,8072,8143,8211,8307,8375,8498,8619,8706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,343,423,490,557,631,707,790,871,939,1018,1096,1171,1258,1344,1419,1493,1567,1654,1726,1801,1870", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "119,198,269,338,418,485,552,626,702,785,866,934,1013,1091,1166,1253,1339,1414,1488,1562,1649,1721,1796,1865,1938"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3431,4253,9786,9857,9926,10006,10542,10609,10683,10759,10842,10923,10991,11152,11230,11305,11392,11478,11553,11627,11701,11889,11961,12036,12105", "endColumns": "68,78,70,68,79,66,66,73,75,82,80,67,78,77,74,86,85,74,73,73,86,71,74,68,72", "endOffsets": "3495,4327,9852,9921,10001,10068,10604,10678,10754,10837,10918,10986,11065,11225,11300,11387,11473,11548,11622,11696,11783,11956,12031,12100,12173"}}]}]}