{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3365,3467,3569,3670,3770,3878,3982,10093", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3462,3564,3665,3765,3873,3977,4096,10189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3451,3512,3587,3662,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3446,3507,3582,3657,3710,3777"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,518,5699,5785,5880,5963,6061,6160,6235,6303,6404,6505,6570,6633,6696,6768,6896,7028,7155,7232,7306,7379,7454,7544,7643,7712,8466,8519,8579,8627,8688,8749,8820,8880,8948,9011,9076,9142,9194,9255,9330,9405,9458", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "330,513,689,5780,5875,5958,6056,6155,6230,6298,6399,6500,6565,6628,6691,6763,6891,7023,7150,7227,7301,7374,7449,7539,7638,7707,7773,8514,8574,8622,8683,8744,8815,8875,8943,9006,9071,9137,9189,9250,9325,9400,9453,9520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7778,7843,7909,7979,8043,8122,8190,8292,8386", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "7838,7904,7974,8038,8117,8185,8287,8381,8461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "694,799,894,994,1076,1176,1293,1378,1456,1547,1640,1735,1829,1923,2016,2111,2206,2297,2389,2473,2583,2689,2789,2897,3003,3105,3266,10009", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "794,889,989,1071,1171,1288,1373,1451,1542,1635,1730,1824,1918,2011,2106,2201,2292,2384,2468,2578,2684,2784,2892,2998,3100,3261,3360,10088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,324,423,526,605,705,794,880,969,1036,1139,1224,1316,1393,1481,1555,1653,1721,1787,1867,1951,2042", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "128,243,319,418,521,600,700,789,875,964,1031,1134,1219,1311,1388,1476,1550,1648,1716,1782,1862,1946,2037,2132"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4101,4179,4294,4370,4469,4572,4651,4751,4840,4926,5015,5082,5185,5270,5362,5439,5527,5601,9525,9593,9659,9739,9823,9914", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "4174,4289,4365,4464,4567,4646,4746,4835,4921,5010,5077,5180,5265,5357,5434,5522,5596,5694,9588,9654,9734,9818,9909,10004"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "46,47,48,49,50,51,52,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3365,3467,3569,3670,3770,3878,3982,10093", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3462,3564,3665,3765,3873,3977,4096,10189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3451,3512,3587,3662,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3446,3507,3582,3657,3710,3777"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,518,5699,5785,5880,5963,6061,6160,6235,6303,6404,6505,6570,6633,6696,6768,6896,7028,7155,7232,7306,7379,7454,7544,7643,7712,8466,8519,8579,8627,8688,8749,8820,8880,8948,9011,9076,9142,9194,9255,9330,9405,9458", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,51,60,74,74,52,66", "endOffsets": "330,513,689,5780,5875,5958,6056,6155,6230,6298,6399,6500,6565,6628,6691,6763,6891,7023,7150,7227,7301,7374,7449,7539,7638,7707,7773,8514,8574,8622,8683,8744,8815,8875,8943,9006,9071,9137,9189,9250,9325,9400,9453,9520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7778,7843,7909,7979,8043,8122,8190,8292,8386", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "7838,7904,7974,8038,8117,8185,8287,8381,8461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "694,799,894,994,1076,1176,1293,1378,1456,1547,1640,1735,1829,1923,2016,2111,2206,2297,2389,2473,2583,2689,2789,2897,3003,3105,3266,10009", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "794,889,989,1071,1171,1288,1373,1451,1542,1635,1730,1824,1918,2011,2106,2201,2292,2384,2468,2578,2684,2784,2892,2998,3100,3261,3360,10088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,324,423,526,605,705,794,880,969,1036,1139,1224,1316,1393,1481,1555,1653,1721,1787,1867,1951,2042", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "128,243,319,418,521,600,700,789,875,964,1031,1134,1219,1311,1388,1476,1550,1648,1716,1782,1862,1946,2037,2132"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4101,4179,4294,4370,4469,4572,4651,4751,4840,4926,5015,5082,5185,5270,5362,5439,5527,5601,9525,9593,9659,9739,9823,9914", "endColumns": "77,114,75,98,102,78,99,88,85,88,66,102,84,91,76,87,73,97,67,65,79,83,90,94", "endOffsets": "4174,4289,4365,4464,4567,4646,4746,4835,4921,5010,5077,5180,5265,5357,5434,5522,5596,5694,9588,9654,9734,9818,9909,10004"}}]}]}