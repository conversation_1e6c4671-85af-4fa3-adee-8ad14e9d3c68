{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7895,7963,8029,8096,8162,8237,8304,8436,8565", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "7958,8024,8091,8157,8232,8299,8431,8560,8649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3471,4273,9702,9774,9842,9922,10464,10531,10605,10682,10765,10845,10915,11081,11161,11236,11324,11411,11486,11562,11637,11833,11909,11986,12056", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3539,4345,9769,9837,9917,9985,10526,10600,10677,10760,10840,10910,10989,11156,11231,11319,11406,11481,11557,11632,11727,11904,11981,12051,12124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3643,3745,3847,3950,4051,4153,11732", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3638,3740,3842,3945,4046,4148,4268,11828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,849,955,1061,1150,1255,1376,1459,1541,1632,1725,1819,1913,2013,2106,2201,2295,2386,2477,2563,2673,2777,2880,2988,3096,3201,3366,10994", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "844,950,1056,1145,1250,1371,1454,1536,1627,1720,1814,1908,2008,2101,2196,2290,2381,2472,2558,2668,2772,2875,2983,3091,3196,3361,3466,11076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,547,5917,6002,6086,6167,6260,6356,6432,6498,6587,6676,6743,6807,6869,6942,7058,7174,7292,7363,7446,7515,7591,7679,7766,7830,8654,8707,8769,8817,8878,8938,9000,9064,9130,9187,9251,9316,9369,9432,9509,9586,9638", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "333,542,736,5997,6081,6162,6255,6351,6427,6493,6582,6671,6738,6802,6864,6937,7053,7169,7287,7358,7441,7510,7586,7674,7761,7825,7890,8702,8764,8812,8873,8933,8995,9059,9125,9182,9246,9311,9364,9427,9504,9581,9633,9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,226,293,391,484,577,654,746,841,917,992,1088,1172,1271,1352,1450,1523,1622,1692,1759,1843,1922,2007", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "121,221,288,386,479,572,649,741,836,912,987,1083,1167,1266,1347,1445,1518,1617,1687,1754,1838,1917,2002,2091"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4350,4421,4521,4588,4686,4779,4872,4949,5041,5136,5212,5287,5383,5467,5566,5647,5745,5818,9990,10060,10127,10211,10290,10375", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "4416,4516,4583,4681,4774,4867,4944,5036,5131,5207,5282,5378,5462,5561,5642,5740,5813,5912,10055,10122,10206,10285,10370,10459"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7895,7963,8029,8096,8162,8237,8304,8436,8565", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "7958,8024,8091,8157,8232,8299,8431,8560,8649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3471,4273,9702,9774,9842,9922,10464,10531,10605,10682,10765,10845,10915,11081,11161,11236,11324,11411,11486,11562,11637,11833,11909,11986,12056", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3539,4345,9769,9837,9917,9985,10526,10600,10677,10760,10840,10910,10989,11156,11231,11319,11406,11481,11557,11632,11727,11904,11981,12051,12124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3643,3745,3847,3950,4051,4153,11732", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3638,3740,3842,3945,4046,4148,4268,11828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,849,955,1061,1150,1255,1376,1459,1541,1632,1725,1819,1913,2013,2106,2201,2295,2386,2477,2563,2673,2777,2880,2988,3096,3201,3366,10994", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "844,950,1056,1145,1250,1371,1454,1536,1627,1720,1814,1908,2008,2101,2196,2290,2381,2472,2558,2668,2772,2875,2983,3091,3196,3361,3466,11076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3384,3447,3524,3601,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3379,3442,3519,3596,3648,3712"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,547,5917,6002,6086,6167,6260,6356,6432,6498,6587,6676,6743,6807,6869,6942,7058,7174,7292,7363,7446,7515,7591,7679,7766,7830,8654,8707,8769,8817,8878,8938,9000,9064,9130,9187,9251,9316,9369,9432,9509,9586,9638", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,52,62,76,76,51,63", "endOffsets": "333,542,736,5997,6081,6162,6255,6351,6427,6493,6582,6671,6738,6802,6864,6937,7053,7169,7287,7358,7441,7510,7586,7674,7761,7825,7890,8702,8764,8812,8873,8933,8995,9059,9125,9182,9246,9311,9364,9427,9504,9581,9633,9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,226,293,391,484,577,654,746,841,917,992,1088,1172,1271,1352,1450,1523,1622,1692,1759,1843,1922,2007", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "121,221,288,386,479,572,649,741,836,912,987,1083,1167,1266,1347,1445,1518,1617,1687,1754,1838,1917,2002,2091"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4350,4421,4521,4588,4686,4779,4872,4949,5041,5136,5212,5287,5383,5467,5566,5647,5745,5818,9990,10060,10127,10211,10290,10375", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "4416,4516,4583,4681,4774,4867,4944,5036,5131,5207,5282,5378,5462,5561,5642,5740,5813,5912,10055,10122,10206,10285,10370,10459"}}]}]}