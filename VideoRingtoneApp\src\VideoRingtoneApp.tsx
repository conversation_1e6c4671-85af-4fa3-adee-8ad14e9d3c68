import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  NativeModules,
  PermissionsAndroid,
  Platform,
  Image,
} from 'react-native';
// import DocumentPicker from 'react-native-document-picker';

const { VideoRingtoneModule } = NativeModules;

interface PermissionStatus {
  READ_PHONE_STATE: boolean;
  SYSTEM_ALERT_WINDOW: boolean;
  CALL_PHONE: boolean;
  ANSWER_PHONE_CALLS: boolean;
  DEFAULT_DIALER: boolean;
  READ_EXTERNAL_STORAGE: boolean;
  READ_MEDIA_VIDEO: boolean;
}

interface AppInfo {
  packageName: string;
  versionName: string;
  versionCode: number;
  targetSdkVersion: number;
  androidVersion: number;
  androidRelease: string;
}

interface VideoFile {
  uri: string;
  name: string;
  size: number;
  type: string;
}

const VideoRingtoneApp: React.FC = () => {
  const [permissions, setPermissions] = useState<PermissionStatus | null>(null);
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState<VideoFile | null>(null);
  const [currentVideoPath, setCurrentVideoPath] = useState<string | null>(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        checkPermissions(),
        loadAppInfo(),
        loadDeviceInfo(),
        loadCurrentVideo(),
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load app data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadDeviceInfo = async () => {
    try {
      const info = await VideoRingtoneModule.getDeviceInfo();
      setDeviceInfo(info);
    } catch (error) {
      console.error('Error loading device info:', error);
    }
  };

  const checkPermissions = async () => {
    try {
      const permissionStatus = await VideoRingtoneModule.checkPermissions();
      setPermissions(permissionStatus);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const loadAppInfo = async () => {
    try {
      const info = await VideoRingtoneModule.getAppInfo();
      setAppInfo(info);
    } catch (error) {
      console.error('Error loading app info:', error);
    }
  };

  const loadCurrentVideo = async () => {
    try {
      const videoPath = await VideoRingtoneModule.getCurrentVideoPath();
      setCurrentVideoPath(videoPath);
    } catch (error) {
      console.error('Error loading current video:', error);
    }
  };

  const requestPhonePermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
          PermissionsAndroid.PERMISSIONS.CALL_PHONE,
        ];

        if (Platform.Version >= 26) {
          permissions.push(PermissionsAndroid.PERMISSIONS.ANSWER_PHONE_CALLS);
        }

        const granted = await PermissionsAndroid.requestMultiple(permissions);

        const allGranted = Object.values(granted).every(
          status => status === PermissionsAndroid.RESULTS.GRANTED
        );

        if (allGranted) {
          Alert.alert('Success', 'Phone permissions granted');
          await checkPermissions();
        } else {
          Alert.alert('Error', 'Some permissions were denied');
        }
      }
    } catch (error) {
      console.error('Error requesting phone permissions:', error);
      Alert.alert('Error', 'Failed to request permissions');
    }
  };

  const requestSystemAlertWindow = async () => {
    try {
      const needsRequest = await VideoRingtoneModule.requestSystemAlertWindowPermission();
      if (needsRequest) {
        Alert.alert(
          'Permission Required',
          'Please enable "Display over other apps" permission for video ringtones to work properly.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {} }
          ]
        );
      } else {
        Alert.alert('Info', 'Permission already granted');
      }
    } catch (error) {
      console.error('Error requesting system alert window permission:', error);
      Alert.alert('Error', 'Failed to request permission');
    }
  };

  const requestDefaultDialer = async () => {
    try {
      const needsRequest = await VideoRingtoneModule.requestDefaultDialerRole();
      if (needsRequest) {
        const isVivo = deviceInfo?.isVivo || false;
        const message = isVivo
          ? 'This will open the default apps settings. Please:\n\n1. Find "Phone app" or "Dialer"\n2. Select "Video Ringtone App"\n3. If not visible, check Settings > Apps > Default apps > Phone app'
          : 'Please set this app as the default dialer to enable call management features.';

        Alert.alert(
          'Set as Default Dialer',
          message,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Continue', onPress: () => {} }
          ]
        );
      } else {
        Alert.alert('Success', 'App is already set as the default dialer');
      }
    } catch (error) {
      console.error('Error requesting default dialer role:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to request default dialer role';

      if (errorMessage.includes('Unable to open dialer settings')) {
        Alert.alert(
          'Manual Setup Required',
          'Please manually set this app as default dialer:\n\n1. Go to Settings > Apps > Default apps\n2. Select "Phone app" or "Dialer"\n3. Choose "Video Ringtone App"\n\nFor Vivo devices, also check Settings > Apps > Video Ringtone App > Set as default',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', errorMessage);
      }
    }
  };

  const openAppSettings = async () => {
    try {
      await VideoRingtoneModule.openAppSettings();
    } catch (error) {
      console.error('Error opening app settings:', error);
      Alert.alert('Error', 'Failed to open app settings');
    }
  };

  const testVideoRingtone = async () => {
    try {
      await VideoRingtoneModule.testVideoRingtone();
      Alert.alert(
        'Test Started',
        'Video ringtone test started. You should see a full-screen video with answer/decline buttons.',
        [
          { text: 'Stop Test', onPress: stopTestVideoRingtone }
        ]
      );
    } catch (error) {
      console.error('Error testing video ringtone:', error);
      Alert.alert('Error', 'Failed to start video ringtone test');
    }
  };

  const stopTestVideoRingtone = async () => {
    try {
      await VideoRingtoneModule.stopTestVideoRingtone();
      Alert.alert('Test Stopped', 'Video ringtone test stopped');
    } catch (error) {
      console.error('Error stopping video ringtone test:', error);
      Alert.alert('Error', 'Failed to stop video ringtone test');
    }
  };

  const requestStoragePermission = async () => {
    try {
      const needsRequest = await VideoRingtoneModule.requestStoragePermission();
      if (needsRequest) {
        Alert.alert(
          'Storage Permission Required',
          'Please grant storage/media access permission to select video files. This will open the app settings where you can enable the permission.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {} }
          ]
        );
      } else {
        Alert.alert('Info', 'Storage permission already granted');
      }
      await checkPermissions();
    } catch (error) {
      console.error('Error requesting storage permission:', error);
      Alert.alert('Error', 'Failed to request storage permission');
    }
  };

  const selectVideoFile = async () => {
    try {
      // Check storage permission first
      await checkPermissions();

      if (Platform.OS === 'android') {
        // Check if we have the required storage permission
        const hasStoragePermission = Platform.Version >= 33
          ? permissions.READ_MEDIA_VIDEO
          : permissions.READ_EXTERNAL_STORAGE;

        if (!hasStoragePermission) {
          Alert.alert(
            'Permission Required',
            'Storage permission is required to select video files. Please grant the permission first.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Grant Permission', onPress: requestStoragePermission }
            ]
          );
          return;
        }
      }

      // Use native file picker
      const result = await VideoRingtoneModule.pickVideoFile();

      if (result && result.success) {
        const videoFile = result.file;

        // Validate file size (max 50MB)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (videoFile.size && videoFile.size > maxSize) {
          Alert.alert('File Too Large', 'Please select a video file smaller than 50MB.');
          return;
        }

        setSelectedVideo({
          uri: videoFile.uri,
          name: videoFile.name || 'Unknown',
          size: videoFile.size || 0,
          type: videoFile.type || 'video/*',
        });

        Alert.alert(
          'Video Selected',
          `Selected: ${videoFile.name}\nSize: ${((videoFile.size || 0) / (1024 * 1024)).toFixed(2)} MB`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Set as Ringtone', onPress: () => setVideoAsRingtone(videoFile) }
          ]
        );
      }
    } catch (error) {
      console.error('Error selecting video file:', error);
      Alert.alert('Error', 'Failed to select video file');
    }
  };

  const setVideoAsRingtone = async (videoFile: any) => {
    try {
      const success = await VideoRingtoneModule.setVideoRingtone(videoFile.uri, videoFile.name);

      if (success) {
        setCurrentVideoPath(videoFile.name);
        Alert.alert('Success', 'Video ringtone has been set successfully!');
      } else {
        Alert.alert('Error', 'Failed to set video as ringtone');
      }
    } catch (error) {
      console.error('Error setting video ringtone:', error);
      Alert.alert('Error', 'Failed to set video as ringtone');
    }
  };

  const removeVideoRingtone = async () => {
    try {
      const success = await VideoRingtoneModule.removeVideoRingtone();

      if (success) {
        setCurrentVideoPath(null);
        setSelectedVideo(null);
        Alert.alert('Success', 'Video ringtone has been removed. Default ringtone will be used.');
      } else {
        Alert.alert('Error', 'Failed to remove video ringtone');
      }
    } catch (error) {
      console.error('Error removing video ringtone:', error);
      Alert.alert('Error', 'Failed to remove video ringtone');
    }
  };

  const renderPermissionStatus = (label: string, granted: boolean) => (
    <View style={styles.permissionRow}>
      <Text style={styles.permissionLabel}>{label}</Text>
      <View style={[styles.statusIndicator, { backgroundColor: granted ? '#4CAF50' : '#F44336' }]}>
        <Text style={styles.statusText}>{granted ? 'Granted' : 'Denied'}</Text>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>Video Ringtone App</Text>
      <Text style={styles.subtitle}>Configure your video ringtone settings</Text>

      {/* Device Info Section */}
      {deviceInfo && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Device Information</Text>
          <Text style={styles.infoText}>Manufacturer: {deviceInfo.manufacturer}</Text>
          <Text style={styles.infoText}>Brand: {deviceInfo.brand}</Text>
          <Text style={styles.infoText}>Model: {deviceInfo.model}</Text>
          <Text style={styles.infoText}>Android: {deviceInfo.release} (API {deviceInfo.sdkVersion})</Text>
          {deviceInfo.isVivo && (
            <Text style={[styles.infoText, { color: '#FF9800', fontWeight: 'bold' }]}>
              ⚠️ Vivo Device Detected - May require additional permission steps
            </Text>
          )}
        </View>
      )}

      {/* App Info Section */}
      {appInfo && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Information</Text>
          <Text style={styles.infoText}>Package: {appInfo.packageName}</Text>
          <Text style={styles.infoText}>Version: {appInfo.versionName} ({appInfo.versionCode})</Text>
          <Text style={styles.infoText}>Target SDK: {appInfo.targetSdkVersion}</Text>
          <Text style={styles.infoText}>Android: {appInfo.androidRelease} (API {appInfo.androidVersion})</Text>
        </View>
      )}

      {/* Permissions Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Permissions Status</Text>
        {permissions && (
          <>
            {renderPermissionStatus('Phone State', permissions.READ_PHONE_STATE)}
            {renderPermissionStatus('Call Phone', permissions.CALL_PHONE)}
            {renderPermissionStatus('Answer Calls', permissions.ANSWER_PHONE_CALLS)}
            {renderPermissionStatus('System Alert Window', permissions.SYSTEM_ALERT_WINDOW)}
            {renderPermissionStatus('Default Dialer', permissions.DEFAULT_DIALER)}
            {Platform.Version >= 33
              ? renderPermissionStatus('Media Video Access', permissions.READ_MEDIA_VIDEO)
              : renderPermissionStatus('Storage Access', permissions.READ_EXTERNAL_STORAGE)
            }
          </>
        )}
      </View>

      {/* Actions Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Setup Actions</Text>

        <TouchableOpacity style={styles.button} onPress={requestPhonePermissions}>
          <Text style={styles.buttonText}>Request Phone Permissions</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={requestStoragePermission}>
          <Text style={styles.buttonText}>
            {Platform.Version >= 33 ? 'Request Media Access' : 'Request Storage Permission'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={requestSystemAlertWindow}>
          <Text style={styles.buttonText}>Request Overlay Permission</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={requestDefaultDialer}>
          <Text style={styles.buttonText}>Set as Default Dialer</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={openAppSettings}>
          <Text style={styles.buttonText}>Open App Settings</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={checkPermissions}>
          <Text style={styles.buttonText}>Refresh Permissions</Text>
        </TouchableOpacity>
      </View>

      {/* Video Selection Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Video Ringtone</Text>

        {currentVideoPath ? (
          <View style={styles.videoInfo}>
            <Text style={styles.videoInfoText}>Current Video: {currentVideoPath}</Text>
            <TouchableOpacity style={[styles.button, styles.removeButton]} onPress={removeVideoRingtone}>
              <Text style={styles.buttonText}>Remove Video</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <Text style={styles.noVideoText}>No video ringtone selected</Text>
        )}

        <TouchableOpacity style={[styles.button, styles.selectButton]} onPress={selectVideoFile}>
          <Text style={styles.buttonText}>Select Video File</Text>
        </TouchableOpacity>

        {selectedVideo && (
          <View style={styles.selectedVideoInfo}>
            <Text style={styles.selectedVideoText}>Selected: {selectedVideo.name}</Text>
            <Text style={styles.selectedVideoText}>Size: {(selectedVideo.size / (1024 * 1024)).toFixed(2)} MB</Text>
          </View>
        )}
      </View>

      {/* Test Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Testing</Text>

        <TouchableOpacity style={[styles.button, styles.testButton]} onPress={testVideoRingtone}>
          <Text style={styles.buttonText}>Test Video Ringtone</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.stopButton]} onPress={stopTestVideoRingtone}>
          <Text style={styles.buttonText}>Stop Test</Text>
        </TouchableOpacity>
      </View>

      {/* Instructions Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Instructions</Text>
        <Text style={styles.instructionText}>
          1. Grant all required permissions above{'\n'}
          2. Set this app as the default dialer{'\n'}
          3. Select a video file from your device storage{'\n'}
          4. Test the video ringtone functionality{'\n'}
          5. When you receive a call, your selected video will play in full screen{'\n'}
          6. Use the answer/decline buttons to manage the call{'\n'}
          {'\n'}
          Supported video formats: MP4, AVI, MKV, MOV, 3GP{'\n'}
          Maximum file size: 50MB{'\n'}
          Recommended: 1080p resolution, 10-30 seconds duration
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  contentContainer: {
    padding: 20,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 50,
  },
  title: {
    color: '#ffffff',
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    color: '#cccccc',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  section: {
    backgroundColor: '#2a2a2a',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  infoText: {
    color: '#cccccc',
    fontSize: 14,
    marginBottom: 5,
  },
  permissionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  permissionLabel: {
    color: '#ffffff',
    fontSize: 16,
    flex: 1,
  },
  statusIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#4CAF50',
  },
  stopButton: {
    backgroundColor: '#F44336',
  },
  selectButton: {
    backgroundColor: '#9C27B0',
  },
  removeButton: {
    backgroundColor: '#FF5722',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  videoInfo: {
    backgroundColor: '#3a3a3a',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  videoInfoText: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  noVideoText: {
    color: '#FF9800',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  selectedVideoInfo: {
    backgroundColor: '#3a3a3a',
    borderRadius: 8,
    padding: 10,
    marginTop: 10,
  },
  selectedVideoText: {
    color: '#81C784',
    fontSize: 14,
    marginBottom: 5,
  },
  instructionText: {
    color: '#cccccc',
    fontSize: 14,
    lineHeight: 20,
  },
});

export default VideoRingtoneApp;
