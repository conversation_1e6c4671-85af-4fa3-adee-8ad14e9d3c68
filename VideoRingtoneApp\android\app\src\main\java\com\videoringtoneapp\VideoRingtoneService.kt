package com.videoringtoneapp

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.os.Vibrator
import android.util.Log
import androidx.core.app.NotificationCompat

class VideoRingtoneService : Service() {

    companion object {
        private const val TAG = "VideoRingtoneService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "video_ringtone_channel"

        const val ACTION_START_VIDEO_RINGTONE = "START_VIDEO_RINGTONE"
        const val ACTION_STOP_VIDEO_RINGTONE = "STOP_VIDEO_RINGTONE"
    }

    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var wakeLock: PowerManager.WakeLock? = null
    private var isRinging = false

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        createNotificationChannel()

        // Initialize vibrator
        vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

        // Initialize wake lock
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            "VideoRingtone:WakeLock"
        )
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service started with action: ${intent?.action}")

        when (intent?.action) {
            ACTION_START_VIDEO_RINGTONE -> {
                val phoneNumber = intent.getStringExtra("phone_number") ?: "Unknown"
                startVideoRingtone(phoneNumber)
            }
            ACTION_STOP_VIDEO_RINGTONE -> {
                stopVideoRingtone()
            }
        }

        return START_NOT_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Video Ringtone Service",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Handles video ringtone playback during incoming calls"
                setSound(null, null)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun startVideoRingtone(phoneNumber: String) {
        if (isRinging) {
            Log.d(TAG, "Already ringing, ignoring start request")
            return
        }

        try {
            isRinging = true

            // Acquire wake lock
            wakeLock?.acquire(30000) // 30 seconds max

            // Start foreground service
            val notification = createNotification(phoneNumber)
            startForeground(NOTIFICATION_ID, notification)

            // Start vibration (only vibration, no audio ringtone)
            startVibration()

            Log.d(TAG, "Video ringtone started for: $phoneNumber")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting video ringtone", e)
            stopVideoRingtone()
        }
    }

    private fun stopVideoRingtone() {
        if (!isRinging) {
            Log.d(TAG, "Not ringing, ignoring stop request")
            return
        }

        try {
            isRinging = false

            // Stop vibration
            stopVibration()

            // Release wake lock
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                }
            }

            // Stop foreground service
            stopForeground(true)
            stopSelf()

            Log.d(TAG, "Video ringtone stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping video ringtone", e)
        }
    }

    // Audio ringtone removed - we only use video ringtone

    private fun startVibration() {
        try {
            vibrator?.let {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val pattern = longArrayOf(0, 1000, 1000)
                    it.vibrate(
                        android.os.VibrationEffect.createWaveform(pattern, 0),
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                            .build()
                    )
                } else {
                    @Suppress("DEPRECATION")
                    val pattern = longArrayOf(0, 1000, 1000)
                    it.vibrate(pattern, 0)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting vibration", e)
        }
    }

    private fun stopVibration() {
        try {
            vibrator?.cancel()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping vibration", e)
        }
    }

    private fun createNotification(phoneNumber: String): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Incoming Call")
            .setContentText("Video ringtone playing for: $phoneNumber")
            .setSmallIcon(android.R.drawable.ic_menu_call)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")
        stopVideoRingtone()
    }
}
