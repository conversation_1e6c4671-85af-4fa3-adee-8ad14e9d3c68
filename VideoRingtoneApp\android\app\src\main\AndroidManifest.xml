<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Additional permissions required for dialer app -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
    <uses-permission android:name="android.permission.BIND_INCALL_SERVICE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>

      <!-- Dialer Activity for handling dialer intents -->
      <activity
        android:name=".DialerActivity"
        android:label="@string/app_name"
        android:theme="@android:style/Theme.NoDisplay"
        android:excludeFromRecents="true"
        android:exported="true">

        <!-- Handle dialer intents -->
        <intent-filter android:priority="1000">
            <action android:name="android.intent.action.DIAL" />
            <category android:name="android.intent.category.DEFAULT" />
        </intent-filter>

        <intent-filter android:priority="1000">
            <action android:name="android.intent.action.DIAL" />
            <category android:name="android.intent.category.DEFAULT" />
            <data android:scheme="tel" />
        </intent-filter>

        <intent-filter android:priority="1000">
            <action android:name="android.intent.action.CALL_PRIVILEGED" />
            <category android:name="android.intent.category.DEFAULT" />
            <data android:scheme="tel" />
        </intent-filter>

        <intent-filter android:priority="1000">
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="tel" />
        </intent-filter>
      </activity>

      <!-- InCallService - REQUIRED for default dialer -->
      <service
        android:name=".VideoRingtoneInCallService"
        android:permission="android.permission.BIND_INCALL_SERVICE"
        android:exported="true">
        <intent-filter>
          <action android:name="android.telecom.InCallService" />
        </intent-filter>
        <!-- This service provides the in-call UI -->
        <meta-data
          android:name="android.telecom.IN_CALL_SERVICE_UI"
          android:value="true" />
        <!-- This service handles ringing for incoming calls -->
        <meta-data
          android:name="android.telecom.IN_CALL_SERVICE_RINGING"
          android:value="true" />
        <meta-data
          android:name="android.telecom.INCLUDE_EXTERNAL_CALLS"
          android:value="true" />
        <meta-data
          android:name="android.telecom.INCLUDE_SELF_MANAGED_CALLS"
          android:value="true" />
      </service>

      <!-- Video Ringtone Service -->
      <service
        android:name=".VideoRingtoneService"
        android:enabled="true"
        android:exported="false"
        android:foregroundServiceType="phoneCall" />

      <!-- Call State Receiver -->
      <receiver
        android:name=".CallStateReceiver"
        android:enabled="true"
        android:exported="true">
        <intent-filter android:priority="1000">
          <action android:name="android.intent.action.PHONE_STATE" />
        </intent-filter>
      </receiver>

      <!-- Video Ringtone Activity -->
      <activity
        android:name=".VideoRingtoneActivity"
        android:theme="@style/VideoRingtoneTheme"
        android:launchMode="singleTop"
        android:excludeFromRecents="true"
        android:showOnLockScreen="true"
        android:turnScreenOn="true"
        android:exported="false" />
    </application>
</manifest>
