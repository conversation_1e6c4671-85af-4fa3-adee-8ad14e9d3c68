# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/RNPermissionsSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/Props.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp"
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/Debug/3n3a1vn3/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
