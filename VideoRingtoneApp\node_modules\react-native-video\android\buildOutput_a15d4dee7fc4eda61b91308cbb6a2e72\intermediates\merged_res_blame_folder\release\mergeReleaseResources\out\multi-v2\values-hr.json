{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "49,50,51,52,53,54,55,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3699,3797,3904,4001,4100,4204,4308,12073", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3792,3899,3996,4095,4199,4303,4420,12169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1019,1114,1221,1307,1411,1530,1615,1697,1788,1881,1976,2070,2170,2263,2358,2453,2544,2635,2721,2825,2937,3038,3143,3257,3359,3528,11344", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1014,1109,1216,1302,1406,1525,1610,1692,1783,1876,1971,2065,2165,2258,2353,2448,2539,2630,2716,2820,2932,3033,3138,3252,3354,3523,3620,11424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8193,8268,8329,8394,8467,8546,8619,8704,8786", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "8263,8324,8389,8462,8541,8614,8699,8781,8854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "48,56,125,126,127,128,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3625,4425,9985,10059,10128,10209,10778,10849,10930,11013,11097,11186,11258,11429,11512,11588,11668,11750,11829,11907,11983,12174,12247,12326,12404", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3694,4504,10054,10123,10204,10271,10844,10925,11008,11092,11181,11253,11339,11507,11583,11663,11745,11824,11902,11978,12068,12242,12321,12399,12480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,234,308,401,498,606,695,784,885,973,1050,1148,1237,1331,1410,1516,1598,1692,1763,1837,1916,2005,2097", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "125,229,303,396,493,601,690,779,880,968,1045,1143,1232,1326,1405,1511,1593,1687,1758,1832,1911,2000,2092,2189"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4509,4584,4688,4762,4855,4952,5060,5149,5238,5339,5427,5504,5602,5691,5785,5864,5970,6052,10276,10347,10421,10500,10589,10681", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "4579,4683,4757,4850,4947,5055,5144,5233,5334,5422,5499,5597,5686,5780,5859,5965,6047,6141,10342,10416,10495,10584,10676,10773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3680,3743,3828,3913,3969", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3675,3738,3823,3908,3964,4032"}, "to": {"startLines": "2,11,16,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,6146,6227,6309,6389,6496,6603,6673,6740,6831,6923,6988,7059,7122,7194,7313,7437,7558,7626,7710,7781,7852,7956,8061,8128,8859,8912,8970,9018,9079,9153,9232,9308,9382,9446,9505,9576,9628,9691,9776,9861,9917", "endLines": "10,15,20,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "340,630,909,6222,6304,6384,6491,6598,6668,6735,6826,6918,6983,7054,7117,7189,7308,7432,7553,7621,7705,7776,7847,7951,8056,8123,8188,8907,8965,9013,9074,9148,9227,9303,9377,9441,9500,9571,9623,9686,9771,9856,9912,9980"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "49,50,51,52,53,54,55,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3699,3797,3904,4001,4100,4204,4308,12073", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3792,3899,3996,4095,4199,4303,4420,12169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,1019,1114,1221,1307,1411,1530,1615,1697,1788,1881,1976,2070,2170,2263,2358,2453,2544,2635,2721,2825,2937,3038,3143,3257,3359,3528,11344", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1014,1109,1216,1302,1406,1525,1610,1692,1783,1876,1971,2065,2165,2258,2353,2448,2539,2630,2716,2820,2932,3033,3138,3252,3354,3523,3620,11424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8193,8268,8329,8394,8467,8546,8619,8704,8786", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "8263,8324,8389,8462,8541,8614,8699,8781,8854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "48,56,125,126,127,128,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3625,4425,9985,10059,10128,10209,10778,10849,10930,11013,11097,11186,11258,11429,11512,11588,11668,11750,11829,11907,11983,12174,12247,12326,12404", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3694,4504,10054,10123,10204,10271,10844,10925,11008,11092,11181,11253,11339,11507,11583,11663,11745,11824,11902,11978,12068,12242,12321,12399,12480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,234,308,401,498,606,695,784,885,973,1050,1148,1237,1331,1410,1516,1598,1692,1763,1837,1916,2005,2097", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "125,229,303,396,493,601,690,779,880,968,1045,1143,1232,1326,1405,1511,1593,1687,1758,1832,1911,2000,2092,2189"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4509,4584,4688,4762,4855,4952,5060,5149,5238,5339,5427,5504,5602,5691,5785,5864,5970,6052,10276,10347,10421,10500,10589,10681", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "4579,4683,4757,4850,4947,5055,5144,5233,5334,5422,5499,5597,5686,5780,5859,5965,6047,6141,10342,10416,10495,10584,10676,10773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3680,3743,3828,3913,3969", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3675,3738,3823,3908,3964,4032"}, "to": {"startLines": "2,11,16,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,6146,6227,6309,6389,6496,6603,6673,6740,6831,6923,6988,7059,7122,7194,7313,7437,7558,7626,7710,7781,7852,7956,8061,8128,8859,8912,8970,9018,9079,9153,9232,9308,9382,9446,9505,9576,9628,9691,9776,9861,9917", "endLines": "10,15,20,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "340,630,909,6222,6304,6384,6491,6598,6668,6735,6826,6918,6983,7054,7117,7189,7308,7432,7553,7621,7705,7776,7847,7951,8056,8123,8188,8907,8965,9013,9074,9148,9227,9303,9377,9441,9500,9571,9623,9686,9771,9856,9912,9980"}}]}]}