# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/d962228e26d34cb24ddc4a817565b97e/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\d962228e26d34cb24ddc4a817565b97e\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\d962228e26d34cb24ddc4a817565b97e\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libappmodules.so

build H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_RelWithDebInfo rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/ComponentDescriptors.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/EventEmitters.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o CMakeFiles/appmodules.dir/d962228e26d34cb24ddc4a817565b97e/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNPermissionsSpec_autolinked_build/react_codegen_RNPermissionsSpec RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so  H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\29c6ac0a1dd6ebb9204efeee5b088e58\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\29c6ac0a1dd6ebb9204efeee5b088e58

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/29c6ac0a1dd6ebb9204efeee5b088e58/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\rnasyncstorage_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\rnasyncstorage_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNPermissionsSpec


#############################################
# Order-only phony target for react_codegen_RNPermissionsSpec

build cmake_object_order_depends_target_react_codegen_RNPermissionsSpec: phony || RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/RNPermissionsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\RNPermissionsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\1ac44fb29917a513567e7eceb3c4dfa7\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\1ac44fb29917a513567e7eceb3c4dfa7

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\1ac44fb29917a513567e7eceb3c4dfa7\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\1ac44fb29917a513567e7eceb3c4dfa7

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec\RNPermissionsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec

build RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNPermissionsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNPermissionsSpec
  DEP_FILE = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir
  OBJECT_FILE_DIR = RNPermissionsSpec_autolinked_build\CMakeFiles\react_codegen_RNPermissionsSpec.dir\react\renderer\components\RNPermissionsSpec



#############################################
# Object library react_codegen_RNPermissionsSpec

build RNPermissionsSpec_autolinked_build/react_codegen_RNPermissionsSpec: phony RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/RNPermissionsSpec-generated.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/ComponentDescriptors.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/1ac44fb29917a513567e7eceb3c4dfa7/EventEmitters.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/Props.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/RNPermissionsSpecJSI-generated.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/ShadowNodes.cpp.o RNPermissionsSpec_autolinked_build/CMakeFiles/react_codegen_RNPermissionsSpec.dir/react/renderer/components/RNPermissionsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNPermissionsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\RNPermissionsSpec_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNPermissionsSpec_autolinked_build/edit_cache: phony RNPermissionsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNPermissionsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\RNPermissionsSpec_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNPermissionsSpec_autolinked_build/rebuild_cache: phony RNPermissionsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\11362e57e5dca720d89d849811f909f0

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03de314beaa0d70218a6841911723387/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\03de314beaa0d70218a6841911723387\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\03de314beaa0d70218a6841911723387

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03de314beaa0d70218a6841911723387/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\03de314beaa0d70218a6841911723387\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\03de314beaa0d70218a6841911723387

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\21bd114425e66d2924540cc301e4613b\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/H_/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\H_\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-safe-area-context\android\build\generated\source\codegen\jni\react\renderer\components\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\H_\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-safe-area-context\android\build\generated\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5df9083334c648aa4c7759e5777753/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5df9083334c648aa4c7759e5777753\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\6f5df9083334c648aa4c7759e5777753


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_RelWithDebInfo safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/11362e57e5dca720d89d849811f909f0/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03de314beaa0d70218a6841911723387/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/03de314beaa0d70218a6841911723387/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/21bd114425e66d2924540cc301e4613b/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/H_/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5df9083334c648aa4c7759e5777753/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\safeareacontext_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\safeareacontext_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad1ac1d7f7dfca2bd78a27f5d6c1c13f/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ad1ac1d7f7dfca2bd78a27f5d6c1c13f\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ad1ac1d7f7dfca2bd78a27f5d6c1c13f\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad1ac1d7f7dfca2bd78a27f5d6c1c13f/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ad1ac1d7f7dfca2bd78a27f5d6c1c13f\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ad1ac1d7f7dfca2bd78a27f5d6c1c13f\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a7a5b6ef63fe8620daea19ac4f5e9acc/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a7a5b6ef63fe8620daea19ac4f5e9acc\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a7a5b6ef63fe8620daea19ac4f5e9acc\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/59be4a5aef4e68cb1a29d7ce698545fb/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\59be4a5aef4e68cb1a29d7ce698545fb\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\59be4a5aef4e68cb1a29d7ce698545fb\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\937f90bb1252508fb2a13819baaae3c0\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\937f90bb1252508fb2a13819baaae3c0\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d005d15700f84c5d28791fb8186f963b/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d005d15700f84c5d28791fb8186f963b\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d005d15700f84c5d28791fb8186f963b\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\937f90bb1252508fb2a13819baaae3c0\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\937f90bb1252508fb2a13819baaae3c0\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d005d15700f84c5d28791fb8186f963b/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d005d15700f84c5d28791fb8186f963b\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d005d15700f84c5d28791fb8186f963b\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/59be4a5aef4e68cb1a29d7ce698545fb/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\59be4a5aef4e68cb1a29d7ce698545fb\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\59be4a5aef4e68cb1a29d7ce698545fb\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libreact_codegen_rnscreens.so

build H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_RelWithDebInfo rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad1ac1d7f7dfca2bd78a27f5d6c1c13f/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ad1ac1d7f7dfca2bd78a27f5d6c1c13f/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a7a5b6ef63fe8620daea19ac4f5e9acc/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/59be4a5aef4e68cb1a29d7ce698545fb/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d005d15700f84c5d28791fb8186f963b/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/937f90bb1252508fb2a13819baaae3c0/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d005d15700f84c5d28791fb8186f963b/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/59be4a5aef4e68cb1a29d7ce698545fb/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\build\intermediates\cxx\RelWithDebInfo\3c1j1m1w\obj\arm64-v8a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\rnscreens_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\rnscreens_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_RelWithDebInfo H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IH:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/48de17ae001fd5f1bcfd4007338aaa12/transformed/react-android-0.79.2-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\RNVectorIconsSpec_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a\RNVectorIconsSpec_autolinked_build && H:\Coding\android-sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BH:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\.cxx\RelWithDebInfo\3c1j1m1w\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libappmodules.so

build libappmodules.so: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libappmodules.so

build libreact_codegen_rnscreens.so: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react_codegen_RNPermissionsSpec: phony RNPermissionsSpec_autolinked_build/react_codegen_RNPermissionsSpec

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rnscreens: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_safeareacontext: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a

build all: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all RNPermissionsSpec_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNVectorIconsSpec_autolinked_build/all

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/RNPermissionsSpec_autolinked_build

build RNPermissionsSpec_autolinked_build/all: phony RNPermissionsSpec_autolinked_build/react_codegen_RNPermissionsSpec

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: H:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/intermediates/cxx/RelWithDebInfo/3c1j1m1w/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/cmake.verify_globs | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/arm64-v8a/CMakeFiles/VerifyGlobs.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/.cxx/RelWithDebInfo/3c1j1m1w/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt H$:/Coding/VibeCoding/video-ringtone/VideoRingtoneApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake H$:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/abis.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/flags.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake H$:/Coding/android-sdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
