{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "H:/Coding/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "H:/Coding/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "H:/Coding/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "H:/Coding/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-14a31eb11e1db0023574.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e518a1d12af6ae48ae1e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6a463e1cf83cd51e2037.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e518a1d12af6ae48ae1e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6a463e1cf83cd51e2037.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-14a31eb11e1db0023574.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}