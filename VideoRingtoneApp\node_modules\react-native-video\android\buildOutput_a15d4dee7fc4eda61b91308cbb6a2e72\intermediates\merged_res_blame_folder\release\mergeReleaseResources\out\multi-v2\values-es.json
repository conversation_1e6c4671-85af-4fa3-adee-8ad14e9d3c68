{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,810,923,1031,1116,1217,1345,1431,1512,1604,1698,1795,1889,1989,2083,2179,2275,2367,2459,2541,2648,2759,2858,2966,3074,3181,3340,11097", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "805,918,1026,1111,1212,1340,1426,1507,1599,1693,1790,1884,1984,2078,2174,2270,2362,2454,2536,2643,2754,2853,2961,3069,3176,3335,3434,11175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3509,3608,3710,3810,3908,4015,4121,11824", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3603,3705,3805,3903,4010,4116,4236,11920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7994,8077,8140,8205,8279,8356,8423,8510,8596", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "8072,8135,8200,8274,8351,8418,8505,8591,8660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,283,353,436,505,572,652,735,822,917,989,1080,1164,1240,1323,1405,1480,1559,1634,1724,1797,1880,1956", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,203,278,348,431,500,567,647,730,817,912,984,1075,1159,1235,1318,1400,1475,1554,1629,1719,1792,1875,1951,2038"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3439,4241,9743,9818,9888,9971,10522,10589,10669,10752,10839,10934,11006,11180,11264,11340,11423,11505,11580,11659,11734,11925,11998,12081,12157", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3504,4319,9813,9883,9966,10035,10584,10664,10747,10834,10929,11001,11092,11259,11335,11418,11500,11575,11654,11729,11819,11993,12076,12152,12239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,316,428,533,629,714,812,916,1008,1077,1198,1289,1388,1464,1558,1639,1731,1800,1872,1945,2022,2116", "endColumns": "74,104,80,111,104,95,84,97,103,91,68,120,90,98,75,93,80,91,68,71,72,76,93,96", "endOffsets": "125,230,311,423,528,624,709,807,911,1003,1072,1193,1284,1383,1459,1553,1634,1726,1795,1867,1940,2017,2111,2208"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4324,4399,4504,4585,4697,4802,4898,4983,5081,5185,5277,5346,5467,5558,5657,5733,5827,5908,10040,10109,10181,10254,10331,10425", "endColumns": "74,104,80,111,104,95,84,97,103,91,68,120,90,98,75,93,80,91,68,71,72,76,93,96", "endOffsets": "4394,4499,4580,4692,4797,4893,4978,5076,5180,5272,5341,5462,5553,5652,5728,5822,5903,5995,10104,10176,10249,10326,10420,10517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3379,3444,3523,3602,3660", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3374,3439,3518,3597,3655,3725"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,6000,6086,6173,6258,6354,6450,6525,6593,6688,6783,6849,6918,6984,7055,7163,7269,7376,7446,7533,7603,7683,7773,7864,7930,8665,8718,8776,8824,8883,8948,9010,9076,9148,9212,9273,9339,9392,9457,9536,9615,9673", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "330,516,703,6081,6168,6253,6349,6445,6520,6588,6683,6778,6844,6913,6979,7050,7158,7264,7371,7441,7528,7598,7678,7768,7859,7925,7989,8713,8771,8819,8878,8943,9005,9071,9143,9207,9268,9334,9387,9452,9531,9610,9668,9738"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,810,923,1031,1116,1217,1345,1431,1512,1604,1698,1795,1889,1989,2083,2179,2275,2367,2459,2541,2648,2759,2858,2966,3074,3181,3340,11097", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "805,918,1026,1111,1212,1340,1426,1507,1599,1693,1790,1884,1984,2078,2174,2270,2362,2454,2536,2643,2754,2853,2961,3069,3176,3335,3434,11175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "47,48,49,50,51,52,53,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3509,3608,3710,3810,3908,4015,4121,11824", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3603,3705,3805,3903,4010,4116,4236,11920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7994,8077,8140,8205,8279,8356,8423,8510,8596", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "8072,8135,8200,8274,8351,8418,8505,8591,8660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,283,353,436,505,572,652,735,822,917,989,1080,1164,1240,1323,1405,1480,1559,1634,1724,1797,1880,1956", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,203,278,348,431,500,567,647,730,817,912,984,1075,1159,1235,1318,1400,1475,1554,1629,1719,1792,1875,1951,2038"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3439,4241,9743,9818,9888,9971,10522,10589,10669,10752,10839,10934,11006,11180,11264,11340,11423,11505,11580,11659,11734,11925,11998,12081,12157", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3504,4319,9813,9883,9966,10035,10584,10664,10747,10834,10929,11001,11092,11259,11335,11418,11500,11575,11654,11729,11819,11993,12076,12152,12239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,316,428,533,629,714,812,916,1008,1077,1198,1289,1388,1464,1558,1639,1731,1800,1872,1945,2022,2116", "endColumns": "74,104,80,111,104,95,84,97,103,91,68,120,90,98,75,93,80,91,68,71,72,76,93,96", "endOffsets": "125,230,311,423,528,624,709,807,911,1003,1072,1193,1284,1383,1459,1553,1634,1726,1795,1867,1940,2017,2111,2208"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4324,4399,4504,4585,4697,4802,4898,4983,5081,5185,5277,5346,5467,5558,5657,5733,5827,5908,10040,10109,10181,10254,10331,10425", "endColumns": "74,104,80,111,104,95,84,97,103,91,68,120,90,98,75,93,80,91,68,71,72,76,93,96", "endOffsets": "4394,4499,4580,4692,4797,4893,4978,5076,5180,5272,5341,5462,5553,5652,5728,5822,5903,5995,10104,10176,10249,10326,10420,10517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3379,3444,3523,3602,3660", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3374,3439,3518,3597,3655,3725"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,6000,6086,6173,6258,6354,6450,6525,6593,6688,6783,6849,6918,6984,7055,7163,7269,7376,7446,7533,7603,7683,7773,7864,7930,8665,8718,8776,8824,8883,8948,9010,9076,9148,9212,9273,9339,9392,9457,9536,9615,9673", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "330,516,703,6081,6168,6253,6349,6445,6520,6588,6683,6778,6844,6913,6979,7050,7158,7264,7371,7441,7528,7598,7678,7768,7859,7925,7989,8713,8771,8819,8878,8943,9005,9071,9143,9207,9268,9334,9387,9452,9531,9610,9668,9738"}}]}]}