http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rn_edit_text_material.xml,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backiconmask.png,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-hdpi/node_modules_reactnavigation_elements_src_assets_backicon.png,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backicon.png,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-xhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-xxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png,${\:app*buildDir}/generated/res/createBundleReleaseJsAndAssets/drawable-xxxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png,${\:app*buildDir}/generated/res/resValues/release/values/gradleResValues.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,+drawable:rn_edit_text_material,0,F;node_modules_reactnavigation_elements_src_assets_backiconmask,1,F;node_modules_reactnavigation_elements_src_assets_backicon,2,F;node_modules_reactnavigation_elements_src_assets_backicon,3,F;node_modules_reactnavigation_elements_src_assets_backicon,4,F;node_modules_reactnavigation_elements_src_assets_backicon,5,F;node_modules_reactnavigation_elements_src_assets_backicon,6,F;+integer:react_native_dev_server_port,7,V40006009f,3f000600da,;"8081";+mipmap:ic_launcher_round,8,F;ic_launcher_round,9,F;ic_launcher_round,10,F;ic_launcher_round,11,F;ic_launcher_round,12,F;ic_launcher,13,F;ic_launcher,14,F;ic_launcher,15,F;ic_launcher,16,F;ic_launcher,17,F;+string:app_name,18,V400010010,**********,;"VideoRingtoneApp";+style:VideoRingtoneTheme,19,V40009014b,c0015046a,;DTheme.AppCompat.NoActionBar,android\:windowFullscreen:true,android\:windowNoTitle:true,android\:windowActionBar:false,android\:windowBackground:@android\:color/black,android\:windowShowWallpaper:false,android\:windowIsTranslucent:false,android\:windowContentOverlay:@null,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:statusBarColor:@android\:color/black,android\:navigationBarColor:@android\:color/black,;AppTheme,19,V400030036,c0006010b,;DTheme.AppCompat.DayNight.NoActionBar,android\:editTextBackground:@drawable/rn_edit_text_material,;