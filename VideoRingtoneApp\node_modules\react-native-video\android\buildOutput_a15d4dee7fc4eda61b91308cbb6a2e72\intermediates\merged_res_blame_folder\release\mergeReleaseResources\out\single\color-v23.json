[{"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_btn_colored_borderless_text_material.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_btn_colored_borderless_text_material.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_color_highlight_material.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_color_highlight_material.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_btn_colored_text_material.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_btn_colored_text_material.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_default.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_default.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_btn_checkable.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_btn_checkable.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_switch_track.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_switch_track.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_spinner.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_spinner.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_edittext.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_edittext.xml"}, {"merged": "com.brentvatne.react.react-native-video-release-37:/color-v23/abc_tint_seek_thumb.xml", "source": "com.brentvatne.react.react-native-video-appcompat-1.7.0-21:/color-v23/abc_tint_seek_thumb.xml"}]