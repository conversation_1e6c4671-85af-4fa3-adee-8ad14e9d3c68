package com.videoringtoneapp

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.RingtoneManager
import android.net.Uri
import android.telecom.Call
import android.telecom.InCallService
import android.util.Log

class VideoRingtoneInCallService : InCallService() {

    companion object {
        private const val TAG = "VideoRingtoneInCallService"
        private const val INCOMING_CALL_CHANNEL_ID = "incoming_calls"
        private const val NOTIFICATION_ID = 1001
    }

    private var audioManager: AudioManager? = null

    private val callCallback = object : Call.Callback() {
        override fun onStateChanged(call: Call, state: Int) {
            super.onStateChanged(call, state)
            Log.d(TAG, "Call state changed: $state")

            when (state) {
                Call.STATE_RINGING -> {
                    Log.d(TAG, "Incoming call detected")
                    handleIncomingCall(call)
                }
                Call.STATE_ACTIVE -> {
                    Log.d(TAG, "Call answered")
                    stopVideoRingtone()
                    dismissIncomingCallNotification()
                }
                Call.STATE_DISCONNECTED -> {
                    Log.d(TAG, "Call ended")
                    stopVideoRingtone()
                    dismissIncomingCallNotification()
                }
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "InCallService created")
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        createNotificationChannel()
    }

    override fun onCallAdded(call: Call) {
        super.onCallAdded(call)
        Log.d(TAG, "Call added: ${call.details}")
        call.registerCallback(callCallback)

        if (call.state == Call.STATE_RINGING) {
            handleIncomingCall(call)
        }
    }

    override fun onCallRemoved(call: Call) {
        super.onCallRemoved(call)
        Log.d(TAG, "Call removed")
        call.unregisterCallback(callCallback)
        stopVideoRingtone()
        dismissIncomingCallNotification()
    }

    private fun createNotificationChannel() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val channel = NotificationChannel(
            INCOMING_CALL_CHANNEL_ID,
            "Incoming Calls",
            NotificationManager.IMPORTANCE_HIGH
        ).apply {
            description = "Notifications for incoming calls"

            // NO SOUND - We handle audio through video ringtone
            setSound(null, null)

            enableVibration(false) // We handle vibration in service
            enableLights(true)
        }

        notificationManager.createNotificationChannel(channel)
    }

    private fun handleIncomingCall(call: Call) {
        try {
            val phoneNumber = call.details.handle?.schemeSpecificPart ?: "Unknown"
            Log.d(TAG, "Handling incoming call from: $phoneNumber")

            // STOP SYSTEM RINGTONE - This is crucial!
            stopSystemRingtone()

            // Show incoming call notification (silent)
            showIncomingCallNotification(phoneNumber)

            // Start video ringtone service
            val serviceIntent = Intent(this, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_START_VIDEO_RINGTONE
                putExtra("phone_number", phoneNumber)
            }
            startForegroundService(serviceIntent)

            // Start video ringtone activity
            val activityIntent = Intent(this, VideoRingtoneActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("phone_number", phoneNumber)
                putExtra("is_incoming_call", true)
            }
            startActivity(activityIntent)

        } catch (e: Exception) {
            Log.e(TAG, "Error handling incoming call", e)
        }
    }

    private fun stopSystemRingtone() {
        try {
            audioManager?.let { am ->
                // Set ringer mode to silent temporarily
                am.ringerMode = AudioManager.RINGER_MODE_SILENT
                Log.d(TAG, "System ringtone stopped")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping system ringtone", e)
        }
    }

    private fun restoreSystemRingtone() {
        try {
            audioManager?.let { am ->
                // Restore normal ringer mode
                am.ringerMode = AudioManager.RINGER_MODE_NORMAL
                Log.d(TAG, "System ringtone restored")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error restoring system ringtone", e)
        }
    }

    private fun showIncomingCallNotification(phoneNumber: String) {
        try {
            // Create intent for full screen incoming call UI
            val fullScreenIntent = Intent(this, VideoRingtoneActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("phone_number", phoneNumber)
                putExtra("is_incoming_call", true)
            }

            val fullScreenPendingIntent = PendingIntent.getActivity(
                this, 0, fullScreenIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val notification = Notification.Builder(this, INCOMING_CALL_CHANNEL_ID)
                .setContentTitle("Incoming Call")
                .setContentText("Call from $phoneNumber")
                .setSmallIcon(android.R.drawable.ic_menu_call)
                .setCategory(Notification.CATEGORY_CALL)
                .setOngoing(true)
                .setPriority(Notification.PRIORITY_HIGH)
                .setFullScreenIntent(fullScreenPendingIntent, true)
                .setContentIntent(fullScreenPendingIntent)
                .build()

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)

        } catch (e: Exception) {
            Log.e(TAG, "Error showing incoming call notification", e)
        }
    }

    private fun dismissIncomingCallNotification() {
        try {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            Log.e(TAG, "Error dismissing notification", e)
        }
    }

    private fun stopVideoRingtone() {
        try {
            // Restore system ringtone
            restoreSystemRingtone()

            // Stop video ringtone service
            val serviceIntent = Intent(this, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_STOP_VIDEO_RINGTONE
            }
            startService(serviceIntent)

            // Finish video ringtone activity
            val activityIntent = Intent(this, VideoRingtoneActivity::class.java).apply {
                action = VideoRingtoneActivity.ACTION_FINISH_ACTIVITY
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(activityIntent)

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping video ringtone", e)
        }
    }
}
