{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7740,7805,7864,7931,7996,8070,8132,8212,8292", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "7800,7859,7926,7991,8065,8127,8207,8287,8349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "48,56,125,126,127,128,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3463,4229,9384,9454,9523,9604,10142,10210,10288,10366,10448,10527,10598,10758,10838,10911,10991,11069,11144,11216,11288,11476,11547,11626,11695", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3527,4302,9449,9518,9599,9667,10205,10283,10361,10443,10522,10593,10671,10833,10906,10986,11064,11139,11211,11283,11370,11542,11621,11690,11765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1838,1948,2066,2137,2214,2283,2344,2434,2523,2587,2650,2704,2775,2823,2884,2943,3010,3071,3134,3195,3252,3318,3370,3424,3492,3560,3614", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1833,1943,2061,2132,2209,2278,2339,2429,2518,2582,2645,2699,2770,2818,2879,2938,3005,3066,3129,3190,3247,3313,3365,3419,3487,3555,3609,3675"}, "to": {"startLines": "2,11,16,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,573,5847,5926,6004,6080,6165,6249,6311,6373,6462,6548,6613,6677,6740,6808,6928,7038,7156,7227,7304,7373,7434,7524,7613,7677,8354,8408,8479,8527,8588,8647,8714,8775,8838,8899,8956,9022,9074,9128,9196,9264,9318", "endLines": "10,15,20,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "329,568,802,5921,5999,6075,6160,6244,6306,6368,6457,6543,6608,6672,6735,6803,6923,7033,7151,7222,7299,7368,7429,7519,7608,7672,7735,8403,8474,8522,8583,8642,8709,8770,8833,8894,8951,9017,9069,9123,9191,9259,9313,9379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,246,315,412,497,574,665,751,850,938,1007,1095,1192,1277,1349,1437,1506,1595,1663,1730,1808,1889,1974", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "131,241,310,407,492,569,660,746,845,933,1002,1090,1187,1272,1344,1432,1501,1590,1658,1725,1803,1884,1969,2060"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4307,4388,4498,4567,4664,4749,4826,4917,5003,5102,5190,5259,5347,5444,5529,5601,5689,5758,9672,9740,9807,9885,9966,10051", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "4383,4493,4562,4659,4744,4821,4912,4998,5097,5185,5254,5342,5439,5524,5596,5684,5753,5842,9735,9802,9880,9961,10046,10137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "807,912,1012,1120,1204,1306,1422,1501,1579,1670,1764,1858,1952,2052,2145,2240,2333,2424,2516,2597,2702,2805,2903,3008,3110,3212,3366,10676", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "907,1007,1115,1199,1301,1417,1496,1574,1665,1759,1853,1947,2047,2140,2235,2328,2419,2511,2592,2697,2800,2898,3003,3105,3207,3361,3458,10753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "49,50,51,52,53,54,55,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3626,3728,3825,3922,4023,4123,11375", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3621,3723,3820,3917,4018,4118,4224,11471"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7740,7805,7864,7931,7996,8070,8132,8212,8292", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "7800,7859,7926,7991,8065,8127,8207,8287,8349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "48,56,125,126,127,128,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3463,4229,9384,9454,9523,9604,10142,10210,10288,10366,10448,10527,10598,10758,10838,10911,10991,11069,11144,11216,11288,11476,11547,11626,11695", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3527,4302,9449,9518,9599,9667,10205,10283,10361,10443,10522,10593,10671,10833,10906,10986,11064,11139,11211,11283,11370,11542,11621,11690,11765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1838,1948,2066,2137,2214,2283,2344,2434,2523,2587,2650,2704,2775,2823,2884,2943,3010,3071,3134,3195,3252,3318,3370,3424,3492,3560,3614", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1833,1943,2061,2132,2209,2278,2339,2429,2518,2582,2645,2699,2770,2818,2879,2938,3005,3066,3129,3190,3247,3313,3365,3419,3487,3555,3609,3675"}, "to": {"startLines": "2,11,16,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,573,5847,5926,6004,6080,6165,6249,6311,6373,6462,6548,6613,6677,6740,6808,6928,7038,7156,7227,7304,7373,7434,7524,7613,7677,8354,8408,8479,8527,8588,8647,8714,8775,8838,8899,8956,9022,9074,9128,9196,9264,9318", "endLines": "10,15,20,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,51,53,67,67,53,65", "endOffsets": "329,568,802,5921,5999,6075,6160,6244,6306,6368,6457,6543,6608,6672,6735,6803,6923,7033,7151,7222,7299,7368,7429,7519,7608,7672,7735,8403,8474,8522,8583,8642,8709,8770,8833,8894,8951,9017,9069,9123,9191,9259,9313,9379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,246,315,412,497,574,665,751,850,938,1007,1095,1192,1277,1349,1437,1506,1595,1663,1730,1808,1889,1974", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "131,241,310,407,492,569,660,746,845,933,1002,1090,1187,1272,1344,1432,1501,1590,1658,1725,1803,1884,1969,2060"}, "to": {"startLines": "57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,129,130,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4307,4388,4498,4567,4664,4749,4826,4917,5003,5102,5190,5259,5347,5444,5529,5601,5689,5758,9672,9740,9807,9885,9966,10051", "endColumns": "80,109,68,96,84,76,90,85,98,87,68,87,96,84,71,87,68,88,67,66,77,80,84,90", "endOffsets": "4383,4493,4562,4659,4744,4821,4912,4998,5097,5185,5254,5342,5439,5524,5596,5684,5753,5842,9735,9802,9880,9961,10046,10137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "807,912,1012,1120,1204,1306,1422,1501,1579,1670,1764,1858,1952,2052,2145,2240,2333,2424,2516,2597,2702,2805,2903,3008,3110,3212,3366,10676", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "907,1007,1115,1199,1301,1417,1496,1574,1665,1759,1853,1947,2047,2140,2235,2328,2419,2511,2592,2697,2800,2898,3003,3105,3207,3361,3458,10753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "49,50,51,52,53,54,55,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3626,3728,3825,3922,4023,4123,11375", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3621,3723,3820,3917,4018,4118,4224,11471"}}]}]}