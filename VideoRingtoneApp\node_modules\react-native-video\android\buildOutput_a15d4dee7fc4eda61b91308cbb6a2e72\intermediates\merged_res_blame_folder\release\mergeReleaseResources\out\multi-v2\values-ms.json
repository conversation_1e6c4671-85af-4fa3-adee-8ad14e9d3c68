{"logs": [{"outputFile": "com.brentvatne.react.react-native-video-release-37:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "693,804,909,1017,1104,1208,1319,1398,1476,1567,1660,1755,1849,1947,2040,2135,2229,2320,2411,2491,2603,2711,2808,2917,3021,3128,3287,10732", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "799,904,1012,1099,1203,1314,1393,1471,1562,1655,1750,1844,1942,2035,2130,2224,2315,2406,2486,2598,2706,2803,2912,3016,3123,3282,3383,10808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "47,48,49,50,51,52,53,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3459,3554,3656,3753,3863,3969,4087,11458", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3549,3651,3748,3858,3964,4082,4197,11554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7845,7915,7979,8045,8110,8188,8254,8344,8427", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "7910,7974,8040,8105,8183,8249,8339,8422,8499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,204,274,342,424,493,567,643,725,808,885,968,1042,1127,1212,1290,1367,1444,1530,1605,1682,1752", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,199,269,337,419,488,562,638,720,803,880,963,1037,1122,1207,1285,1362,1439,1525,1600,1677,1747,1821"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,139,140,141,142,143,144,145,146,148,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3388,4202,9567,9637,9705,9787,10340,10414,10490,10572,10655,10813,10896,10970,11055,11140,11218,11295,11372,11559,11634,11711,11781", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "3454,4275,9632,9700,9782,9851,10409,10485,10567,10650,10727,10891,10965,11050,11135,11213,11290,11367,11453,11629,11706,11776,11850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,235,310,414,514,607,699,791,885,979,1043,1155,1248,1340,1414,1506,1585,1686,1753,1819,1892,1968,2067", "endColumns": "78,100,74,103,99,92,91,91,93,93,63,111,92,91,73,91,78,100,66,65,72,75,98,102", "endOffsets": "129,230,305,409,509,602,694,786,880,974,1038,1150,1243,1335,1409,1501,1580,1681,1748,1814,1887,1963,2062,2165"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4280,4359,4460,4535,4639,4739,4832,4924,5016,5110,5204,5268,5380,5473,5565,5639,5731,5810,9856,9923,9989,10062,10138,10237", "endColumns": "78,100,74,103,99,92,91,91,93,93,63,111,92,91,73,91,78,100,66,65,72,75,98,102", "endOffsets": "4354,4455,4530,4634,4734,4827,4919,5011,5105,5199,5263,5375,5468,5560,5634,5726,5805,5906,9918,9984,10057,10133,10232,10335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3290,3352,3428,3504,3566", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3285,3347,3423,3499,3561,3635"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,521,5911,5994,6078,6155,6246,6339,6412,6481,6577,6671,6735,6798,6863,6936,7042,7151,7256,7323,7405,7475,7546,7630,7715,7782,8504,8557,8615,8663,8724,8788,8850,8911,8977,9040,9099,9165,9217,9279,9355,9431,9493", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "329,516,688,5989,6073,6150,6241,6334,6407,6476,6572,6666,6730,6793,6858,6931,7037,7146,7251,7318,7400,7470,7541,7625,7710,7777,7840,8552,8610,8658,8719,8783,8845,8906,8972,9035,9094,9160,9212,9274,9350,9426,9488,9562"}}]}, {"outputFile": "com.brentvatne.react.react-native-video-mergeReleaseResources-35:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c2455bfab1cfa3eca9fababdaf610ea7\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "693,804,909,1017,1104,1208,1319,1398,1476,1567,1660,1755,1849,1947,2040,2135,2229,2320,2411,2491,2603,2711,2808,2917,3021,3128,3287,10732", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "799,904,1012,1099,1203,1314,1393,1471,1562,1655,1750,1844,1942,2035,2130,2224,2315,2406,2486,2598,2706,2803,2912,3016,3123,3282,3383,10808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53fd33931d11466b8971a3a1b9d808f4\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "47,48,49,50,51,52,53,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3459,3554,3656,3753,3863,3969,4087,11458", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3549,3651,3748,3858,3964,4082,4197,11554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\edf011c1fc31440de40f8de1f0ce09ca\\transformed\\media3-exoplayer-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7845,7915,7979,8045,8110,8188,8254,8344,8427", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "7910,7974,8040,8105,8183,8249,8339,8422,8499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\48de17ae001fd5f1bcfd4007338aaa12\\transformed\\react-android-0.79.2-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,204,274,342,424,493,567,643,725,808,885,968,1042,1127,1212,1290,1367,1444,1530,1605,1682,1752", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,199,269,337,419,488,562,638,720,803,880,963,1037,1122,1207,1285,1362,1439,1525,1600,1677,1747,1821"}, "to": {"startLines": "46,54,123,124,125,126,133,134,135,136,137,139,140,141,142,143,144,145,146,148,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3388,4202,9567,9637,9705,9787,10340,10414,10490,10572,10655,10813,10896,10970,11055,11140,11218,11295,11372,11559,11634,11711,11781", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "3454,4275,9632,9700,9782,9851,10409,10485,10567,10650,10727,10891,10965,11050,11135,11213,11290,11367,11453,11629,11706,11776,11850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfda21954fd43dece074a90fbf362b2b\\transformed\\media3-session-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,235,310,414,514,607,699,791,885,979,1043,1155,1248,1340,1414,1506,1585,1686,1753,1819,1892,1968,2067", "endColumns": "78,100,74,103,99,92,91,91,93,93,63,111,92,91,73,91,78,100,66,65,72,75,98,102", "endOffsets": "129,230,305,409,509,602,694,786,880,974,1038,1150,1243,1335,1409,1501,1580,1681,1748,1814,1887,1963,2062,2165"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4280,4359,4460,4535,4639,4739,4832,4924,5016,5110,5204,5268,5380,5473,5565,5639,5731,5810,9856,9923,9989,10062,10138,10237", "endColumns": "78,100,74,103,99,92,91,91,93,93,63,111,92,91,73,91,78,100,66,65,72,75,98,102", "endOffsets": "4354,4455,4530,4634,4734,4827,4919,5011,5105,5199,5263,5375,5468,5560,5634,5726,5805,5906,9918,9984,10057,10133,10232,10335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af07a135a4ef7c0df946877d933b0dcc\\transformed\\media3-ui-1.4.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3290,3352,3428,3504,3566", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3285,3347,3423,3499,3561,3635"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,521,5911,5994,6078,6155,6246,6339,6412,6481,6577,6671,6735,6798,6863,6936,7042,7151,7256,7323,7405,7475,7546,7630,7715,7782,8504,8557,8615,8663,8724,8788,8850,8911,8977,9040,9099,9165,9217,9279,9355,9431,9493", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "329,516,688,5989,6073,6150,6241,6334,6407,6476,6572,6666,6730,6793,6858,6931,7037,7146,7251,7318,7400,7470,7541,7625,7710,7777,7840,8552,8610,8658,8719,8783,8845,8906,8972,9035,9094,9160,9212,9274,9350,9426,9488,9562"}}]}]}