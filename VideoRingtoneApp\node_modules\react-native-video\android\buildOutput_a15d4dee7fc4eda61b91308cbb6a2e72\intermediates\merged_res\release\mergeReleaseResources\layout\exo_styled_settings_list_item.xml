<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="@dimen/exo_setting_width"
    android:minHeight="@dimen/exo_settings_height"
    android:background="?android:attr/selectableItemBackground"
    android:layoutDirection="locale"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/exo_icon"
        android:layout_width="@dimen/exo_settings_icon_size"
        android:layout_height="@dimen/exo_settings_icon_size"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="12dp"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/exo_settings_height"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:paddingEnd="4dp"
        android:paddingRight="4dp"
        android:gravity="center|start"
        android:layoutDirection="locale"
        android:orientation="vertical">

        <TextView
            android:id="@+id/exo_main_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/exo_white"
            android:textDirection="locale"
            android:textSize="@dimen/exo_settings_main_text_size"/>

        <TextView
            android:id="@+id/exo_sub_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/exo_white_opacity_70"
            android:textDirection="locale"
            android:textSize="@dimen/exo_settings_sub_text_size"/>
    </LinearLayout>

</LinearLayout>
